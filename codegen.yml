overwrite: true
schema: "./src/web/graphql/schema.graphql.ts"
documents: ["./src/web/**/*.tsx"]
ignoreNoDocuments: true
emitLegacyCommonJSImports: false
generates:
  ./src/web/graphql/types.generated.ts:
    plugins:
      - add:
          content: >
            /* eslint-disable */
            import { GraphQLFieldExtensions } from "graphql";
            export interface ResolverWithResolve<TResult, TParent, TContext, TArgs> {
               extensions?: GraphQLFieldExtensions<TResult, TContext, TArgs>;
               resolve?: ResolverFn<TR<PERSON>ult, TParent, TContext, TArgs>;
            }
      - "typescript"
      - "typescript-operations"
      - "typescript-resolvers"
    config:
      enumsAsConst: true
      nonOptionalTypename: true
      scalars:
        DateTime: Date
    hooks:
      afterOneFileWrite:
        [
          "sed -i '' 's/export type ResolverWithResolve/type __ResolverWithResolve/g'",
        ]
  ./src/web/graphql/client.generated/:
    preset: "client"
    plugins:
      - add:
          content: "/* eslint-disable */"
    config:
      dedupeFragments: true
      enumsAsConst: true
      scalars:
        DateTime: Date | string
