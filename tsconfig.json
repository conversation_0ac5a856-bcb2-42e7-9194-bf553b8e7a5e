{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "composite": true, "forceConsistentCasingInFileNames": true, "incremental": true, "jsx": "react", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "node", "noImplicitReturns": true, "outDir": "out", "skipLibCheck": true, "strict": true, "target": "ES2022", "tsBuildInfoFile": ".tsbuildinfo"}, "ts-node": {"swc": true, "transpileOnly": true}, "include": ["**/*"], "exclude": ["node_modules"]}