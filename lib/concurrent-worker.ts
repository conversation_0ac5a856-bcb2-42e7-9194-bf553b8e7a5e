export interface ConcurrentResource<T> {
  take(): Promise<T>;
  release(t: T, fail: boolean): void;
  isOutOfCapacity(): boolean;
  abort(): void;
}

export class AbortError extends Error {}

export async function runConcurrently<Args extends unknown[]>(
  fn: (...args: Args) => Promise<void>,
  resources: { [K in keyof Args]: ConcurrentResource<Args[K]> },
  { numberOfWorkers }: { numberOfWorkers: number },
): Promise<void> {
  const workers: Promise<void>[] = [];

  for (let i = 0; i < numberOfWorkers; i += 1) {
    workers.push(
      spawnWorker(fn, resources).catch((error) => {
        if (error instanceof AbortError) {
          return;
        }

        throw error;
      }),
    );
  }

  await Promise.all(workers);
}

async function spawnWorker<Args extends unknown[]>(
  fn: (...args: Args) => Promise<void>,
  resources: { [K in keyof Args]: ConcurrentResource<Args[K]> },
): Promise<void> {
  // eslint-disable-next-line no-constant-condition
  while (true) {
    const args: Args = (await Promise.all(
      resources.map((resource) => resource.take()),
    )) as Args;

    let didError = false;

    try {
      await fn(...args);
    } catch (error) {
      didError = true;
    } finally {
      for (const [index, resource] of resources.entries()) {
        resource.release(args[index], didError);
      }

      if (resources.some((resource) => resource.isOutOfCapacity())) {
        for (const resource of resources) {
          resource.abort();
        }

        // eslint-disable-next-line no-unsafe-finally
        break;
      }
    }
  }
}
