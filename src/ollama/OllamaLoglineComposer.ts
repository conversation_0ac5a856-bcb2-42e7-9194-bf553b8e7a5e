import assert from "assert";
import { Ollama } from "ollama";

import {
  ComposeLoglineResult,
  LoglineComposeError,
  LoglineComposer,
} from "../jobs/ComposeLoglinesJob.js";

export default class OllamaLoglineComposer implements LoglineComposer {
  private ollama: Ollama;

  constructor() {
    this.ollama = new Ollama();
  }

  async composeLogline(plot: string): Promise<ComposeLoglineResult> {
    const messages = [
      {
        role: "system",
        content: `
          Ты редактор сайта о кино. Тебе будет предоставлен сюжет фильма. Составь короткий (до 12 слов) однопредложный премиз на русском языке для фильма. Премиз не должен содержать спойлеров, то есть упоминания того, как кончается история.

          Примеры:
          "Три солдата пытаются найти себя в обществе после Второй мировой войны"
          "Сын главы мафиозного клана оказывается втянутым в мир преступности против своей воли"
          "Мальчик пытается найти свою мать в преступном мире"

          Выведи короткий (до 12 слов) однопредложный премиз в формате JSON:
          {
            "premise": "<ПРЕМИС НА РУССКОМ>"
          }
        `,
      },
      {
        role: "user",
        content: plot,
      },
    ];
    let rawPremise: string | null = null;

    while (messages.length < 7) {
      const { message } = await this.ollama.chat({
        model: "qwen2.5:14b",
        messages,
      });

      messages.push(message);
      rawPremise = message.content;

      if (rawPremise.split(" ").length >= 20 || rawPremise.length >= 120) {
        messages.push({
          content: "Перепиши премиз, чтобы он содержал максимум 14 слов",
          role: "user",
        });
      } else {
        break;
      }
    }

    if (rawPremise === null) {
      throw new LoglineComposeError(`Ollama couldn't generate premise`, "");
    }

    const { premise } = JSON.parse(rawPremise) as { premise: string };

    assert(
      typeof premise === "string",
      new LoglineComposeError(
        `Ollama response doesn't match to shape {"logline": string}`,
        rawPremise.trim(),
      ),
    );

    return {
      logline: premise,
      model: "qwen2.5:14b",
      prompt: "",
      system: "",
      options: {},
    };
  }
}
