import * as fs from "fs";
import * as path from "path";
import assert from "assert";
import * as cssSelect from "css-select";
import * as domhandler from "domhandler";
import * as domutils from "domutils";
import * as htmlparser2 from "htmlparser2";
import * as puppeteer from "puppeteer";

import {
  KinopoiskGateway,
  KinopoiskGatewayFetchUserResponse,
  KinopoiskJob,
} from "../jobs/PullKinopoiskMarksJob.js";

interface ConstuctorOptions {
  puppeteer: {
    userDataDir?: string;
    executablePath?: string;
  };
}

export default class WebdriverKinopoiskGateway implements KinopoiskGateway {
  private browser?: puppeteer.Browser;
  private page?: puppeteer.Page;

  constructor(private options: ConstuctorOptions) {}

  async terminate(): Promise<void> {
    if (!this.page?.isClosed()) {
      await this.page?.close();
    }
    await this.browser?.close();

    this.page = undefined;
    this.browser = undefined;
  }

  async fetchUser(
    job: KinopoiskJob,
  ): Promise<KinopoiskGatewayFetchUserResponse | null> {
    const votePages: KinopoiskGatewayFetchUserResponse[] = [];

    for await (const votePage of this.votesPages(job)) {
      votePages.push(votePage);

      if (
        job.marks.after &&
        votePage.marks.some((mark) => mark.timestamp <= job.marks.after!)
      ) {
        break;
      }
    }

    if (votePages.length === 0) {
      return null;
    }

    const user = votePages.reduceRight((acc, x) => ({
      ...x,
      marks: uniqBy((mark) => mark.movie.id, [...x.marks, ...acc.marks]),
    }));

    if (job.friends != null) {
      if (job.friends.limit > 30) {
        user.friends = [];

        for await (const friendPage of this.friendsPages(job)) {
          user.friends.push(...friendPage.friends);

          if (user.friends.length >= job.friends.limit) {
            break;
          }
        }
      }

      user.friends = user.friends.slice(0, job.friends.limit);
    }

    if (job.marks.after) {
      user.marks = user.marks.filter(
        (mark) => mark.timestamp > job.marks.after!,
      );
    }

    return user;
  }

  private async *votesPages(
    job: KinopoiskJob,
  ): AsyncIterable<KinopoiskGatewayFetchUserResponse> {
    const perPage = 200;
    let lastPage = 1;

    for (let page = 1; page <= lastPage; page += 1) {
      const route: ProfileVotesRoute = {
        orderBy: page === 1 ? undefined : "date",
        perPage: page === 1 ? perPage : undefined,
        page: page === 1 ? undefined : page,
        profileId: job.id,
        type: "profileVotes",
      };

      const html = await this.getHtml(routeToString(route));
      const document = htmlparser2.parseDocument(html);
      const scrapedAt = new Date();

      const error = cssSelect.selectOne(
        ".error-page__container-left h1",
        document,
      );

      if (error) {
        break;
      }

      const name =
        cssSelect.selectOne(".profile_name_link_box a", document) ??
        cssSelect.selectOne(".profile_name a", document);

      assert(name, "No matches for '.profile_name_link_box a'");

      yield {
        id: job.id,

        friends: cssSelect
          .selectAll(".profile_name:not(.profile_name_link_box)", document)
          .map((node) => {
            const anchor: domhandler.Element | null = cssSelect.selectOne(
              "a",
              node,
            );

            assert(
              anchor,
              "No matches for '.profile_name:not(.profile_name_link_box) a'",
            );

            const href = domutils.getAttributeValue(anchor, "href");

            assert(
              href,
              "No 'href' for '.profile_name:not(.profile_name_link_box) a'",
            );

            const id = /https:\/\/www.kinopoisk.ru\/user\/(\d+)\//.exec(
              `https://www.kinopoisk.ru${href}`,
            )?.[1];

            assert(
              id,
              `Couldn't parse id from URL: "https://www.kinopoisk.ru${href}"`,
            );

            return {
              id,
              name: domutils.textContent(anchor),
            };
          }),

        name: domutils.textContent(name),

        marks: cssSelect
          .selectAll(".profileFilmsList .item", document)
          .map(
            (
              node,
            ): KinopoiskGatewayFetchUserResponse["marks"][number] | null => {
              const titleElement: domhandler.Element | null =
                cssSelect.selectOne(".nameRus a", node);
              const dateElement: domhandler.Element | null =
                cssSelect.selectOne(".date", node);
              const voteElement: domhandler.Element | null =
                cssSelect.selectOne(".vote", node);

              assert(
                titleElement,
                "No matches for '.profileFilmsList .item .nameRus a'",
              );
              assert(
                dateElement,
                "No matches for '.profileFilmsList .item .date'",
              );
              assert(
                voteElement,
                "No matches for '.profileFilmsList .item .vote'",
              );

              const titleHref = domutils.getAttributeValue(
                titleElement,
                "href",
              );
              const titleText = domutils.textContent(titleElement);

              if (
                titleText.includes("(сериал") ||
                titleText.includes("(мини-сериал")
              ) {
                return null;
              }

              const title =
                /^(.*) \((\d*)\)$/.exec(titleText)?.slice(1) ?? // Астенический синдром (1989)
                /^(.*)$/.exec(titleText)?.slice(1); // Способный ученик

              const dateText = domutils.textContent(dateElement);
              const date = /(\d{2})\.(\d{2})\.(\d{4}), (\d{2}):(\d{2})/.exec(
                dateText,
              );

              const voteText = domutils.textContent(voteElement);

              assert(
                title,
                `Unknown format for '.profileFilmsList .item .nameRus a': "${titleText}"`,
              );
              assert(
                titleHref,
                "No 'href' for '.profileFilmsList .item .nameRus a'",
              );
              assert(
                date,
                `Unknown format for '.profileFilmsList .item .date': "${dateText}"`,
              );

              const year = title[1] ? Number(title[1]) : null;
              const score = voteText ? Number.parseInt(voteText, 10) : null;
              const timestamp = new Date(
                Number(date[3]),
                Number(date[2]) - 1,
                Number(date[1]),
                Number(date[4]),
                Number(date[5]),
              );

              assert(
                Number.isFinite(year) || year === null,
                `Invalid year in '.profileFilmsList .item .nameRus a': ${titleText}`,
              );
              assert(
                Number.isFinite(score) || score === null,
                `Invalid score in '.profileFilmsList .item .vote': ${voteText}`,
              );
              assert(
                Number.isFinite(timestamp.valueOf()),
                `Unknown format for '.profileFilmsList .item .date': "${dateText}"`,
              );

              const id =
                /https:\/\/www.kinopoisk.ru\/(?:series|film)\/(\d+)\//.exec(
                  `https://www.kinopoisk.ru${titleHref}`,
                )?.[1];

              assert(
                id,
                `Couldn't parse id from URL: "https://www.kinopoisk.ru${titleHref}"`,
              );

              return {
                movie: {
                  id,
                  title: title[0],
                  year,
                },
                score,
                timestamp,
                scrapedAt,
              };
            },
          )
          .filter(
            (
              mark,
            ): mark is KinopoiskGatewayFetchUserResponse["marks"][number] =>
              mark !== null,
          ),
      };

      const pagesFromTo = cssSelect.selectOne(".pagesFromTo", document);

      if (!pagesFromTo) {
        break;
      }

      const matches = /.* из (\d+)/.exec(domutils.textContent(pagesFromTo));

      assert(matches, "Text doesn't match to pattern '.* из (d+)'");

      const marksCount = Number(matches[1]);

      lastPage = Math.ceil(marksCount / perPage);
    }
  }

  private async *friendsPages(job: KinopoiskJob): AsyncIterable<{
    friends: KinopoiskGatewayFetchUserResponse["friends"][number][];
  }> {
    const route: ProfileFriendsRoute = {
      perPage: 200,
      profileId: job.id,
      type: "profileFriends",
    };
    const html = await this.getHtml(routeToString(route));
    const document = htmlparser2.parseDocument(html);
    const rows: domhandler.Element[] = cssSelect.selectAll(
      "div > div > p.profile_name > a",
      document,
    );

    assert(rows.length > 0, "No matches for 'div > div > p.profile_name > a'");

    yield {
      friends: rows.map((row) => {
        const name = domutils.textContent(row);
        const href = domutils.getAttributeValue(row, "href");

        assert(href, "No 'href' for 'div > div > p.profile_name > a'");

        const id = /https:\/\/www.kinopoisk.ru\/user\/(\d+)\//.exec(
          `https://www.kinopoisk.ru${href}`,
        )?.[1];

        assert(
          id,
          `Couldn't parse id from URL: "https://www.kinopoisk.ru${href}"`,
        );

        return {
          id,
          name,
        };
      }),
    };
  }

  private async getHtml(url: string): Promise<string> {
    const page = await retry(async (attempt) => {
      if (attempt > 0) {
        await this.terminate();
      }

      const browserPage = await this.getBrowserPage();

      await browserPage.setRequestInterception(true);

      browserPage.on("request", (interceptedRequest) => {
        if (interceptedRequest.url().includes("amcharts_v2.8.2.js")) {
          interceptedRequest.abort().catch(() => {});
        } else {
          interceptedRequest.continue().catch(() => {});
        }
      });

      await browserPage.setViewport({
        width: 1024,
        height: 768,
      });

      const selector = `[href*='${new URL(url).pathname}']`;
      const canMakeNativeTransition = Boolean(await browserPage.$(selector));

      if (canMakeNativeTransition && attempt === 0) {
        await browserPage.click(selector);
        await browserPage.waitForNavigation({
          waitUntil: "domcontentloaded",
          timeout: 60_000,
        });
      } else {
        await browserPage.goto(url, {
          waitUntil: "domcontentloaded",
          timeout: 60_000,
        });
      }

      return browserPage;
    }, 3);

    const captcha = await page.$("[data-testid='checkbox-captcha']");

    if (captcha) {
      throw new Error("Encountered captcha form");
    }

    return page.content();
  }

  private async getBrowserPage(): Promise<puppeteer.Page> {
    if (this.page) {
      return this.page;
    }

    this.browser = await this.getBrowser();
    this.page = await this.browser.newPage();

    return this.page;
  }

  private async getBrowser(): Promise<puppeteer.Browser> {
    if (this.options.puppeteer.userDataDir) {
      await fs.promises
        .unlink(path.join(this.options.puppeteer.userDataDir, "SingletonLock"))
        .catch(() => {});
    }

    return puppeteer.launch({
      executablePath: this.options.puppeteer.executablePath,
      headless: "new",
      userDataDir: this.options.puppeteer.userDataDir,
      protocolTimeout: 360_000,
    });
  }
}

function uniqBy<T>(fn: (t: T) => string, items: T[]): T[] {
  const hashes = new Set<string>();
  const result: T[] = [];

  items.forEach((item) => {
    const hash = fn(item);

    if (!hashes.has(hash)) {
      result.push(item);
      hashes.add(hash);
    }
  });

  return result;
}

export interface ProfileRoute {
  profileId: string;
  type: "profile";
}

export interface ProfileVotesRoute {
  genre?: "films";
  orderBy?: "date";
  page?: number;
  perPage?: 200;
  profileId: string;
  type: "profileVotes";
}

export interface ProfileFriendsRoute {
  page?: number;
  perPage?: 200;
  profileId: string;
  type: "profileFriends";
}

export type Route = ProfileRoute | ProfileVotesRoute | ProfileFriendsRoute;

export function routeFromString(str: string): Route | null {
  const profile = /https:\/\/www\.kinopoisk\.ru\/user\/(\d+)\//.exec(str);

  if (profile) {
    return {
      profileId: profile[1],
      type: "profile",
    };
  }

  return null;
}

export function routeToString(route: Route): string {
  if (route.type === "profileVotes") {
    let url = `https://www.kinopoisk.ru/user/${route.profileId}/votes/list`;

    if (route.orderBy) {
      url += `/ord/${route.orderBy}`;
    }

    if (route.genre) {
      url += `/genre/${route.genre}`;
    }

    if (route.page) {
      url += `/page/${route.page}`;
    }

    if (route.perPage) {
      url += `/perpage/${route.perPage}/vs/all/vsnc/1`;
    }

    return `${url}/#list`;
  }

  if (route.type === "profileFriends") {
    let url = `https://www.kinopoisk.ru/community/cf_user/${route.profileId}`;

    if (route.page) {
      url += `/page/${route.page}`;
    }

    if (route.perPage) {
      url += `/perpage/${route.perPage}`;
    }

    return url;
  }

  return `https://www.kinopoisk.ru/user/${route.profileId}/`;
}

async function retry<T>(
  fn: (attempt: number) => Promise<T>,
  attempts: number,
): Promise<T> {
  let attempt = 1;

  // eslint-disable-next-line no-constant-condition
  while (true) {
    try {
      return await fn(attempt - 1);
    } catch (error) {
      const attemptsLeft = attempts - attempt;

      if (attemptsLeft <= 0) {
        throw error;
      }

      attempt += 1;
    }
  }
}
