import { type TypedDocumentNode } from "@graphql-typed-document-node/core";
import {
  useMutation as useReactQueryMutation,
  type UseMutationOptions,
  type UseMutationResult,
  useQuery as useReactQueryQuery,
  type UseQueryOptions,
  type UseQueryResult,
} from "@tanstack/react-query";
import { type ExecutionResult, print } from "graphql";
import * as React from "react";

export { type DocumentType } from "./client.generated/gql.js";
export * from "./client.generated/graphql.js";
export {
  type FragmentType,
  graphql,
  useFragment,
} from "./client.generated/index.js";

export type GraphQlFetcher = typeof defaultFetcher;

export const GraphQlFetcherContext =
  React.createContext<GraphQlFetcher>(defaultFetcher);

export function useQuery<TResult, TVariables, TError>(
  document: TypedDocumentNode<TResult, TVariables>,
  variables?: TVariables,
  options?: UseQueryOptions<TResult, TError>,
): UseQueryResult<TResult, TError> {
  const fetcher = React.useContext(GraphQlFetcherContext);

  return useReactQueryQuery<TResult, TError>(
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
    [(document.definitions[0] as any).name.value, variables],
    () => fetcher(document, variables),
    options,
  );
}

export function useMutation<TResult, TVariables, TError>(
  document: TypedDocumentNode<TResult, TVariables>,
  options?: UseMutationOptions<TResult, TError, TVariables>,
): UseMutationResult<TResult, TError, TVariables> {
  const fetcher = React.useContext(GraphQlFetcherContext);

  return useReactQueryMutation<TResult, TError, TVariables>(
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
    [(document.definitions[0] as any).name.value],
    (variables) => fetcher(document, variables),
    options,
  );
}

export async function defaultFetcher<TResult, TVariables>(
  document: TypedDocumentNode<TResult, TVariables>,
  variables?: TVariables,
): Promise<TResult> {
  const response = await fetch("/graphql", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      query: print(document),
      variables,
    }),
  });

  const json = (await response.json()) as ExecutionResult<TResult>;

  if (json.errors) {
    const { message } = json.errors[0];

    throw new Error(message);
  }

  return json.data as TResult;
}
