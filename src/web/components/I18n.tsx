export function pluralize(
  n: number,
  forms: { one: string; few: string; many: string; other: string },
): string {
  if (n % 10 === 1 && n % 100 !== 11) {
    return forms.one;
  }

  if (n % 10 >= 2 && n % 10 <= 4 && !(n % 100 >= 12 && n % 100 <= 14)) {
    return forms.few;
  }

  if (
    n % 10 === 0 ||
    (n % 10 >= 5 && n % 10 <= 9) ||
    (n % 100 >= 11 && n % 100 <= 14)
  ) {
    return forms.many;
  }

  return forms.other;
}
