import classNames from "classnames";
import * as React from "react";

interface Props {
  className?: string;
  items: {
    label: string;
    href?: string;
    selected: boolean;
  }[];
}

const SegmentedControl: React.FC<Props> = ({ className, items }) => {
  const sparse = items.length > 5;

  return (
    <ul
      className={classNames(className, "inline", {
        "space-y-1 space-x-1": sparse,
      })}
    >
      {items.map((item, index) => {
        const clsx = classNames("px-1.5 inline-block pb-0.5", {
          "bg-indigo-100": item.selected,
          "bg-zinc-100": !item.selected,
          "rounded-l": sparse || index === 0,
          "rounded-r": sparse || index === items.length - 1,
        });

        return item.href ? (
          <a
            // eslint-disable-next-line react/no-array-index-key
            key={index}
            href={item.href}
            className={classNames(
              clsx,
              "transition-colors hover:bg-zinc-200 hover:transition-none",
            )}
          >
            {item.label}
          </a>
        ) : (
          // eslint-disable-next-line react/no-array-index-key
          <span key={index} className={classNames(clsx, "cursor-default")}>
            {item.label}
          </span>
        );
      })}
    </ul>
  );
};

export default SegmentedControl;
