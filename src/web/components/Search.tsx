/* eslint-disable no-underscore-dangle */
import classNames from "classnames";
import * as React from "react";

import { graphql, useQuery } from "../graphql/client.js";
import { useRouter } from "./Router.js";

interface SearchContextValue {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const SearchContext = React.createContext<SearchContextValue>({
  open: false,
  setOpen: () => {},
});

const GetSearchResults = graphql(/* GraphQL */ `
  query GetSearchResults($text: String!) {
    search(text: $text) {
      label
      results(text: $text) {
        ... on MovieSearchResult {
          __typename
          movie {
            __typename
            id
            kinopoiskId
            slug
            title
            directors {
              fullName
            }
            bestMarksPercentage
            goodMarksPercentage
            topPositionAllTime
            images {
              sizes {
                height
                url
                width
              }
            }
            viewed
            currentMark {
              mark
            }
            year
          }
        }
        ... on PersonSearchResult {
          __typename
          person {
            __typename
            fullName
            id
            slug
            images {
              sizes {
                height
                url
                width
              }
            }
            movies(sort: BEST_FIRST) {
              title
            }
          }
        }
      }
    }
  }
`);

export function useSearch(): SearchContextValue {
  const context = React.useContext(SearchContext);

  return context;
}

export const SearchProvider: React.FC<{ children: React.ReactNode }> = (
  props,
) => {
  const [searchOpened, setSearchOpened] = React.useState(false);
  const bodyRef = React.useRef<HTMLBodyElement | null>(null);

  const onKeyPress = React.useCallback((event: KeyboardEvent) => {
    if (
      event.target !== window.document.body ||
      !/^[A-Za-zА-Яа-я0-9]$/.test(event.key) ||
      event.metaKey ||
      event.ctrlKey
    ) {
      return;
    }

    setSearchOpened(true);
  }, []);

  React.useEffect(() => {
    bodyRef.current = window.document.body as HTMLBodyElement;
  });

  React.useEffect(() => {
    if (searchOpened) {
      bodyRef.current?.classList.add("overflow-hidden");
    } else {
      bodyRef.current?.classList.remove("overflow-hidden");
    }
  }, [searchOpened]);

  React.useEffect(() => {
    if (searchOpened) {
      return () => {};
    }

    window.document.addEventListener("keypress", onKeyPress);

    return () => {
      window.document.removeEventListener("keypress", onKeyPress);
    };
  }, [searchOpened, onKeyPress]);

  const context: SearchContextValue = React.useMemo(
    () => ({
      open: searchOpened,
      setOpen: setSearchOpened,
    }),
    [searchOpened, setSearchOpened],
  );

  return (
    <SearchContext.Provider value={context}>
      {props.children}
    </SearchContext.Provider>
  );
};

const SearchPanel: React.FC = () => {
  const search = useSearch();
  const router = useRouter();
  const paranjaRef = React.useRef<HTMLDivElement | null>(null);
  const [searchText, setSearchText] = React.useState("");
  const [activeItemId, setActiveItemId] = React.useState<string | null>(null);
  const { data, isLoading, isRefetching } = useQuery(
    GetSearchResults,
    {
      text: searchText,
    },
    {
      keepPreviousData: true,
    },
  );
  const itemRefs = React.useRef<Record<string, HTMLAnchorElement>>({});
  const scrollableRef = React.useRef<HTMLDivElement | null>(null);
  const items = React.useMemo(
    () =>
      data?.search
        .map((group) =>
          group.results.map((result) =>
            result.__typename === "MovieSearchResult"
              ? result.movie
              : result.__typename === "PersonSearchResult"
              ? result.person
              : (undefined as never),
          ),
        )
        .reduce((acc, x) => [...acc, ...x], []) ?? [],
    [data],
  );
  const activeItemIndex = items.findIndex((item) => item.id === activeItemId);
  const activeItem = activeItemIndex === -1 ? null : items[activeItemIndex];

  React.useEffect(() => {
    if (!data || data.search.length === 0) {
      setActiveItemId(null);

      return;
    }

    const firstResult = data.search[0].results[0];

    if (firstResult.__typename === "MovieSearchResult") {
      setActiveItemId(firstResult.movie.id);
    } else if (firstResult.__typename === "PersonSearchResult") {
      setActiveItemId(firstResult.person.id);
    }
  }, [data]);

  const offsetActiveItem = React.useCallback(
    (offset: number) => {
      if (activeItemIndex === -1) {
        return;
      }

      const newActiveIndex = Math.min(
        Math.max(activeItemIndex + offset, 0),
        items.length,
      );
      const newActiveId = items[newActiveIndex].id;

      setActiveItemId(newActiveId);

      const ref = itemRefs.current[newActiveId];
      const scrollable = scrollableRef.current;

      if (ref && scrollable) {
        const isOutsideViewport =
          ref.offsetTop < scrollable.scrollTop ||
          ref.offsetTop > scrollable.scrollTop + scrollable.offsetHeight;

        if (isOutsideViewport) {
          scrollable.scrollTo({
            left: 0,
            top: ref.offsetTop,
          });
        }
      }
    },
    [setActiveItemId, scrollableRef, itemRefs, items, activeItemIndex],
  );

  const onClose = React.useCallback(() => {
    search.setOpen(false);
  }, [search]);
  const onKeyPress = React.useCallback(
    (event: KeyboardEvent) => {
      if (event.key === "ArrowUp") {
        offsetActiveItem(-1);
      } else if (event.key === "ArrowDown") {
        offsetActiveItem(+1);
      } else if (event.key === "Escape") {
        event.preventDefault();
        onClose();
      } else if (event.key === "Enter") {
        if (!activeItem) {
          return;
        }

        const href =
          activeItem.__typename === "Movie"
            ? activeItem.slug
              ? router.stringify("/movies/:movieSlug", {
                  movieSlug: activeItem.slug,
                })
              : router.stringify("/movies/by-kinopoisk-id/:kinopoiskId", {
                  kinopoiskId: activeItem.kinopoiskId,
                })
            : router.stringify("/people/:personSlug", {
                personSlug: activeItem.slug,
              });

        window.open(href, "_blank");
      }
    },
    [offsetActiveItem, activeItem, onClose, router],
  );

  React.useEffect(() => {
    window.document.addEventListener("keydown", onKeyPress);

    return () => window.document.removeEventListener("keydown", onKeyPress);
  });

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
    <div
      className="fixed inset-0 z-20 bg-black/70 antialiased"
      ref={paranjaRef}
      onClick={(event) => {
        if (event.target === paranjaRef.current) {
          onClose();
        }
      }}
    >
      <div className="relative z-30 mx-auto mt-[60px] w-[600px] overflow-hidden rounded-2xl bg-white">
        <input
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus
          placeholder="Поиск"
          className="mb-[1px] h-[56px] w-full border-b border-zinc-200 pl-[56px] text-3xl leading-none text-zinc-900 placeholder:text-zinc-500"
          value={searchText}
          onChange={(event) => {
            setSearchText(event.target.value);
          }}
          onKeyDown={(event) => {
            if (event.key === "ArrowUp" || event.key === "ArrowDown") {
              event.preventDefault();
            }
          }}
        />
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute top-[13px] left-4 h-8 w-8 text-zinc-500"
        >
          <g clipPath="url(#clip0_109_815)">
            <path
              d="M15.213 13.799L11.208 9.794C11.8862 8.82953 12.2498 7.67905 12.249 6.5C12.249 4.97501 11.6432 3.51247 10.5649 2.43414C9.48655 1.3558 8.02402 0.75 6.49902 0.75C4.97403 0.75 3.51149 1.3558 2.43316 2.43414C1.35483 3.51247 0.749023 4.97501 0.749023 6.5C0.749023 8.02499 1.35483 9.48753 2.43316 10.5659C3.51149 11.6442 4.97403 12.25 6.49902 12.25C7.67807 12.2508 8.82856 11.8872 9.79302 11.209L13.798 15.214L15.213 13.799ZM2.25002 6.501C2.25002 5.94275 2.35998 5.38997 2.57361 4.87421C2.78724 4.35846 3.10037 3.88983 3.49511 3.49509C3.88985 3.10035 4.35848 2.78722 4.87424 2.57359C5.38999 2.35996 5.94277 2.25 6.50102 2.25C7.05927 2.25 7.61206 2.35996 8.12781 2.57359C8.64357 2.78722 9.11219 3.10035 9.50694 3.49509C9.90168 3.88983 10.2148 4.35846 10.4284 4.87421C10.6421 5.38997 10.752 5.94275 10.752 6.501C10.752 7.62844 10.3042 8.70969 9.50694 9.50691C8.70972 10.3041 7.62846 10.752 6.50102 10.752C5.37359 10.752 4.29233 10.3041 3.49511 9.50691C2.6979 8.70969 2.25002 7.62844 2.25002 6.501Z"
              fill="currentColor"
            />
          </g>
          <defs>
            <clipPath id="clip0_109_815">
              <rect width="16" height="16" fill="white" />
            </clipPath>
          </defs>
        </svg>
        {searchText === "" ? (
          <div className="flex h-[244px] w-full items-center justify-center text-center text-zinc-500">
            Это окно можно открыть
            <br />
            начав вводить текст на странице
          </div>
        ) : (typeof data !== "undefined" &&
            data !== null &&
            data.search.length > 0) ||
          isLoading ||
          isRefetching ? (
          <div className="flex max-h-[calc(100vh-176px)] min-h-[244px] w-full">
            <div
              ref={scrollableRef}
              className="relative max-h-[calc(100vh-176px)] min-h-[244px] w-full overflow-y-scroll pb-4"
            >
              {data?.search.map((group) => (
                <React.Fragment key={group.label}>
                  <h2 className="px-4 pt-2 text-xs text-zinc-500 uppercase">
                    {group.label}
                  </h2>
                  <ul className="mt-[1px] mb-3">
                    {group.results.map((result) =>
                      result.__typename === "MovieSearchResult" ? (
                        <a
                          key={result.movie.id}
                          className={classNames(
                            "mx-2 block h-[74px] cursor-pointer rounded-md px-2 py-2",
                            {
                              "bg-indigo-600 text-white":
                                result.movie.id === activeItemId,
                            },
                          )}
                          ref={(ref) => {
                            if (ref) {
                              itemRefs.current[result.movie.id] = ref;
                            } else {
                              delete itemRefs.current[result.movie.id];
                            }
                          }}
                          href={
                            result.movie.slug
                              ? router.stringify("/movies/:movieSlug", {
                                  movieSlug: result.movie.slug,
                                })
                              : router.stringify(
                                  "/movies/by-kinopoisk-id/:kinopoiskId",
                                  {
                                    kinopoiskId: result.movie.kinopoiskId,
                                  },
                                )
                          }
                          target="_blank"
                          onMouseEnter={() => {
                            setActiveItemId(result.movie.id);
                          }}
                        >
                          <span className="relative float-left mr-3 inline-block overflow-hidden rounded-xs">
                            {result.movie.images.length > 0 ? (
                              <img
                                className="h-[58px] w-[104px]"
                                alt={result.movie.title}
                                src={result.movie.images[0].sizes[0].url}
                                width={result.movie.images[0].sizes[0].width}
                                height={result.movie.images[0].sizes[0].height}
                              />
                            ) : (
                              <div className="h-[58px] w-[104px] bg-zinc-100" />
                            )}
                            <span
                              className={classNames(
                                "absolute -bottom-px left-0 space-x-1.5 pr-1 pl-0.5 text-xs font-bold",
                                {
                                  "bg-indigo-600 text-white":
                                    result.movie.id === activeItemId,
                                  "bg-white": result.movie.id !== activeItemId,
                                },
                              )}
                            >
                              {result.movie.topPositionAllTime ? (
                                <span>
                                  №&nbsp;{result.movie.topPositionAllTime}
                                </span>
                              ) : null}
                              {result.movie.bestMarksPercentage ? (
                                <span
                                  className={classNames({
                                    "text-pink-600":
                                      result.movie.id !== activeItemId,
                                  })}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    className="mr-0.5 inline h-[0.7lh] w-[0.7lh] align-middle"
                                  >
                                    <path
                                      stroke="none"
                                      d="M0 0h24v24H0z"
                                      fill="none"
                                    />
                                    <path d="M6.979 3.074a6 6 0 0 1 4.988 1.425l.037 .033l.034 -.03a6 6 0 0 1 4.733 -1.44l.246 .036a6 6 0 0 1 3.364 10.008l-.18 .185l-.048 .041l-7.45 7.379a1 1 0 0 1 -1.313 .082l-.094 -.082l-7.493 -7.422a6 6 0 0 1 3.176 -10.215z" />
                                  </svg>
                                  {result.movie.bestMarksPercentage}%
                                </span>
                              ) : null}
                              {result.movie.topPositionAllTime == null &&
                              result.movie.goodMarksPercentage ? (
                                <span
                                  className={classNames({
                                    "text-indigo-500":
                                      result.movie.id !== activeItemId &&
                                      result.movie.goodMarksPercentage >= 65,
                                    "text-zinc-500":
                                      result.movie.id !== activeItemId &&
                                      result.movie.goodMarksPercentage < 65,
                                  })}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    className="mr-0.5 inline h-[0.7lh] w-[0.7lh]"
                                  >
                                    <path
                                      stroke="none"
                                      d="M0 0h24v24H0z"
                                      fill="none"
                                    />
                                    <path d="M13 3a3 3 0 0 1 2.995 2.824l.005 .176v4h2a3 3 0 0 1 2.98 2.65l.015 .174l.005 .176l-.02 .196l-1.006 5.032c-.381 1.626 -1.502 2.796 -2.81 2.78l-.164 -.008h-8a1 1 0 0 1 -.993 -.883l-.007 -.117l.001 -9.536a1 1 0 0 1 .5 -.865a2.998 2.998 0 0 0 1.492 -2.397l.007 -.202v-1a3 3 0 0 1 3 -3z" />
                                    <path d="M5 10a1 1 0 0 1 .993 .883l.007 .117v9a1 1 0 0 1 -.883 .993l-.117 .007h-1a2 2 0 0 1 -1.995 -1.85l-.005 -.15v-7a2 2 0 0 1 1.85 -1.995l.15 -.005h1z" />
                                  </svg>
                                  {result.movie.goodMarksPercentage}%
                                </span>
                              ) : null}
                            </span>
                          </span>
                          <div className="pt-1 text-lg">
                            {result.movie.viewed ? (
                              <div
                                className={classNames("float-right", {
                                  "text-zinc-400":
                                    result.movie.id !== activeItemId,
                                  "text-indigo-300":
                                    result.movie.id === activeItemId,
                                })}
                              >
                                {result.movie.currentMark
                                  ? `Моя оценка: ${result.movie.currentMark.mark}`
                                  : "просмотрен"}
                              </div>
                            ) : null}
                            <div className="truncate">{result.movie.title}</div>
                            {result.movie.directors?.length > 0 ||
                            result.movie.year ? (
                              <div
                                className={classNames("mt-0.5 truncate", {
                                  "text-zinc-400":
                                    result.movie.id !== activeItemId,
                                  "text-indigo-300":
                                    result.movie.id === activeItemId,
                                })}
                              >
                                {result.movie.directors
                                  .map((director) => director.fullName)
                                  .join(", ")}
                                {result.movie.directors.length > 0 &&
                                result.movie.year
                                  ? ", "
                                  : null}
                                {result.movie.year}
                              </div>
                            ) : null}
                          </div>
                        </a>
                      ) : result.__typename === "PersonSearchResult" ? (
                        <a
                          key={result.person.id}
                          className={classNames(
                            "mx-2 block h-[62px] cursor-pointer rounded-md px-2 py-2",
                            {
                              "bg-indigo-600 text-white":
                                result.person.id === activeItemId,
                            },
                          )}
                          ref={(ref) => {
                            if (ref) {
                              itemRefs.current[result.person.id] = ref;
                            } else {
                              delete itemRefs.current[result.person.id];
                            }
                          }}
                          href={router.stringify("/people/:personSlug", {
                            personSlug: result.person.slug,
                          })}
                          target="_blank"
                          onMouseEnter={() => {
                            setActiveItemId(result.person.id);
                          }}
                        >
                          <span className="relative float-left mr-2 inline-block overflow-hidden rounded-xs">
                            {result.person.images.length > 0 ? (
                              <img
                                className="h-[46px] w-[46px]"
                                alt={result.person.fullName}
                                src={result.person.images[0].sizes[0].url}
                                width={result.person.images[0].sizes[0].width}
                                height={result.person.images[0].sizes[0].height}
                              />
                            ) : (
                              <div className="h-[46px] w-[46px] bg-zinc-100" />
                            )}
                          </span>
                          <div className="text-lg">
                            <div className="truncate">
                              {result.person.fullName}
                            </div>
                            {result.person.movies.length > 0 ? (
                              <div
                                className={classNames("mt-0.5 truncate", {
                                  "text-zinc-400":
                                    result.person.id !== activeItemId,
                                  "text-indigo-300":
                                    result.person.id === activeItemId,
                                })}
                              >
                                {result.person.movies
                                  .map((movie) => movie.title)
                                  .join(", ")}
                              </div>
                            ) : null}
                          </div>
                        </a>
                      ) : null,
                    )}
                  </ul>
                </React.Fragment>
              ))}
            </div>
          </div>
        ) : (
          <div className="flex h-[244px] w-full text-zinc-500">
            <div className="flex h-[244px] w-full items-center justify-center">
              Ничего не найдено
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const Search: React.FC = () => {
  const { open } = useSearch();

  return open ? <SearchPanel /> : null;
};

export default Search;
