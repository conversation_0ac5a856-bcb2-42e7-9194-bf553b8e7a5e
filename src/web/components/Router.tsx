import * as React from "react";
import UrlPattern from "url-pattern";

type RouterContextValue = {
  [P in keyof PatternParamsMap]: {
    pattern: P;
    params: PatternParamsMap[P];
    Component: React.FC;
    push: (pathname: string) => void;
    replace: (pathname: string) => void;
    rewrite: (pathname: string) => void;
    stringify: <K extends keyof PatternParamsMap>(
      pattern: K,
      values: PatternParamsMap[K],
    ) => string;
    url: URL;
  };
}[keyof PatternParamsMap];

const RouterContext = React.createContext<RouterContextValue>({
  pattern: "/",
  params: {},
  Component: () => null,
  push: () => {},
  replace: () => {},
  rewrite: () => {},
  stringify: () => "/",
  url: null as unknown as URL,
});

export function useRouter(): RouterContextValue {
  const context = React.useContext(RouterContext);

  return context;
}

export type Route = [pattern: keyof PatternParamsMap, Component: React.FC];

export interface MatchResult<
  P extends keyof PatternParamsMap = keyof PatternParamsMap,
> {
  pattern: P;
  Component: React.FC;
  params: PatternParamsMap[P];
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface PatternParamsMap {}

/* eslint-disable no-param-reassign */
const Router: React.FC<{
  routes: Route[];
  children: (context: RouterContextValue) => React.ReactNode;
  initialURL: URL;
  onURLChange?: (url: URL, match: MatchResult) => void;
}> = (props) => {
  const [url, setURL] = React.useState(props.initialURL);

  const matchers = React.useMemo(
    () =>
      props.routes.reduce<Record<string, UrlPattern | undefined>>((acc, x) => {
        acc[x[0]] = new UrlPattern(x[0]);

        return acc;
      }, {}),
    [props.routes],
  );

  const match = React.useCallback(
    <P extends keyof PatternParamsMap>(someURL: URL): MatchResult<P> | null => {
      const route = props.routes.find(
        ([p]) => p === "*" || matchers[p]?.match(someURL.pathname),
      );

      if (!route) {
        return null;
      }

      const pattern = route[0] as P;

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const params = matchers[pattern]!.match(
        someURL.pathname,
      ) as PatternParamsMap[P];

      for (const [key, value] of Object.entries(params)) {
        (params as Record<string, string>)[key] = decodeURIComponent(value);
      }

      return {
        pattern,
        Component: route[1],
        params,
      };
    },
    [props.routes, matchers],
  );

  const stringify = React.useCallback(
    <K extends keyof PatternParamsMap>(
      pattern: K,
      values: PatternParamsMap[K],
    ) => {
      const route = props.routes.find((r) => r[0] === pattern);

      if (!route) {
        throw new TypeError(`Cannot resolve route: ${pattern}`);
      }

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      return matchers[route[0]]!.stringify(values);
    },
    [props.routes, matchers],
  );

  const onURLChange = React.useCallback(
    (newURL: URL) => {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      props.onURLChange?.(newURL, match(newURL)!);
    },
    [props, match],
  );

  onURLChange(url);

  const replace = React.useCallback(
    (pathname: string) => {
      const newURL = new URL(pathname, url);

      setURL(newURL);
      onURLChange(newURL);
      window.history.replaceState(null, "", newURL);
    },
    [url, onURLChange],
  );

  const push = React.useCallback(
    (pathname: string) => {
      const newURL = new URL(pathname, url);

      setURL(newURL);
      onURLChange(newURL);
      window.history.pushState(null, "", newURL);
    },
    [url, onURLChange],
  );

  const rewrite = React.useCallback(
    (pathname: string) => {
      const newURL = new URL(pathname, url);

      if (typeof window !== "undefined") {
        setURL(newURL);
      }
      onURLChange(newURL);
    },
    [url, onURLChange],
  );

  const context = React.useMemo(<
    P extends keyof PatternParamsMap,
  >(): RouterContextValue => {
    const {
      pattern,
      Component,
      params,
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    } = match<P>(url)!;

    return {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-assignment
      pattern: pattern as any,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-assignment
      params: params as any,
      Component,
      push,
      replace,
      rewrite,
      stringify,
      url,
    };
  }, [url, match, push, replace, rewrite, stringify]);

  const onPopState = React.useCallback(() => {
    const newURL = new URL(window.location.href);

    setURL(newURL);
    onURLChange(newURL);
  }, [onURLChange]);

  React.useEffect(() => {
    window.addEventListener("popstate", onPopState);

    return () => {
      window.removeEventListener("popstate", onPopState);
    };
  });

  return (
    <RouterContext.Provider value={context}>
      {props.children(context)}
    </RouterContext.Provider>
  );
};
/* eslint-enable no-param-reassign */

export const Rewrite: React.FC<{ to: { pathname: string } }> = (props) => {
  const router = useRouter();

  React.useEffect(() => {
    router.rewrite(props.to.pathname);
  }, [router, props.to.pathname]);

  if (typeof window === "undefined") {
    router.rewrite(props.to.pathname);
  }

  return null;
};

export default Router;
