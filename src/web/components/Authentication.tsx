import * as webauthn from "@simplewebauthn/browser";
import type { PublicKeyCredentialCreationOptionsJSON } from "@simplewebauthn/typescript-types";
import * as React from "react";

import { graphql, useQuery } from "../graphql/client.js";

const GetMe = graphql(/* GraphQL */ `
  query GetMe {
    me {
      name
      slug
      kinopoiskUrl
      isDemoAccount
    }
  }
`);

type AuthenticationContextValue =
  | {
      state: "authenticated";
      user: {
        name: string;
        slug: string;
        href: string;
      };
    }
  | {
      state: "unauthenticated";
      user: {
        name: string;
        slug: string;
        href: string;
      };
      authenticate: () => void;
      invite: (options: InviteOptions) => void;
      register: () => void;
    };

interface InviteOptions {
  email: string;
  kinopoiskUrl: string;
  name: string;
}

const AuthenticationContext = React.createContext<AuthenticationContextValue>({
  state: "unauthenticated",
  user: {
    name: "demo",
    slug: "demo",
    href: "...",
  },
  authenticate: () => {},
  invite: () => {},
  register: () => {},
});

export const AuthenticationProvider: React.FC<{ children: React.ReactNode }> = (
  props,
) => {
  const meQuery = useQuery(GetMe);

  const authentication: AuthenticationContextValue = React.useMemo(
    () =>
      meQuery.data?.me.isDemoAccount
        ? {
            state: "unauthenticated" as const,
            user: {
              name: meQuery.data?.me.name ?? "demo",
              slug: meQuery.data?.me.slug ?? "demo",
              href: meQuery.data?.me.kinopoiskUrl ?? "...",
            },
            authenticate: () => {
              void (async () => {
                const options = (await (
                  await fetch("/webauthn/generate_authentication_options", {
                    method: "POST",
                  })
                ).json()) as PublicKeyCredentialCreationOptionsJSON;
                const authn = await webauthn.startAuthentication(options);

                const response = await fetch(
                  "/webauthn/verify_authentication",
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(authn),
                  },
                );

                window.location.href = response.url;
              })();
            },
            invite: (options: InviteOptions) => {
              void (async () => {
                await fetch("/invite", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify(options),
                });
              })();
            },
            register: () => {
              void (async () => {
                const options = (await (
                  await fetch("/webauthn/generate_registration_options", {
                    method: "POST",
                  })
                ).json()) as PublicKeyCredentialCreationOptionsJSON;
                const registration = await webauthn.startRegistration(options);

                await fetch("/webauthn/verify_registration", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify(registration),
                });

                void meQuery.refetch();
              })();
            },
          }
        : {
            state: "authenticated" as const,
            user: {
              name: meQuery.data?.me.name ?? "demo",
              slug: meQuery.data?.me.slug ?? "demo",
              href: meQuery.data?.me.kinopoiskUrl ?? "...",
            },
          },
    [meQuery],
  );

  return (
    <AuthenticationContext.Provider value={authentication}>
      {props.children}
    </AuthenticationContext.Provider>
  );
};

export function useAuthentication(): AuthenticationContextValue {
  const context = React.useContext(AuthenticationContext);

  return context;
}
