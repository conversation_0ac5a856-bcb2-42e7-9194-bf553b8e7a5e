import * as React from "react";

const Join: React.FC<{ children: React.ReactNode; delimiter: string }> = (
  props,
) => {
  const children: React.ReactNode[] = [];

  React.Children.forEach(props.children, (child) => {
    if (child !== null) {
      children.push(child);
      children.push(props.delimiter);
    }
  });

  children.pop();

  return <span>{children}</span>;
};

export default Join;
