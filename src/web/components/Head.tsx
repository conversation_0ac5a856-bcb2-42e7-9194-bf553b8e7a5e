import * as React from "react";
import * as ReactDom from "react-dom";

export interface HeadState {
  canonical?: string;
  headFragment: React.ReactElement;
}

export function createEmptyHeadState(): HeadState {
  return {
    // eslint-disable-next-line react/jsx-no-useless-fragment
    headFragment: <></>,
  };
}

const HeadStateContext = React.createContext<HeadState | null>(null);

export const HeadStateProvider = HeadStateContext.Provider;

const Head: React.FC<{ children: React.ReactNode }> = (props) => {
  const headState = React.useContext(HeadStateContext);
  const [head, setHead] = React.useState<HTMLHeadElement | null>(null);

  if (headState) {
    // eslint-disable-next-line react/jsx-no-useless-fragment
    headState.headFragment = <>{props.children}</>;

    React.Children.forEach(props.children, (child) => {
      if (React.isValidElement(child) && child.type === "link" && child.props) {
        const childProps = (child as React.ReactHTMLElement<HTMLLinkElement>)
          .props;

        if (childProps.rel === "canonical" && childProps.href) {
          headState.canonical = childProps.href;
        }
      }
    });
  }

  React.useEffect(() => {
    setHead(document.head);
  }, []);

  return head ? ReactDom.createPortal(props.children, head) : null;
};

export default Head;
