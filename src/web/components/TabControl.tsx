import classNames from "classnames";
import * as React from "react";

interface Props {
  className?: string;
  items: {
    label: string;
    href?: string;
    selected: boolean;
  }[];
}

const Tab: React.FC<Props> = ({ className, items }) => {
  return (
    <ul className={classNames(className, "inline space-x-3")}>
      {items.map((item, index) => {
        const clsx = classNames("inline-block", {
          "border-b-2 border-black": item.selected,
          "hover:border-b-2 hover:border-black": !item.selected,
        });

        return item.href ? (
          <a
            // eslint-disable-next-line react/no-array-index-key
            key={index}
            href={item.href}
            className={classNames(clsx)}
          >
            {item.label}
          </a>
        ) : (
          // eslint-disable-next-line react/no-array-index-key
          <span key={index} className={classNames(clsx, "cursor-default")}>
            {item.label}
          </span>
        );
      })}
    </ul>
  );
};

export default Tab;
