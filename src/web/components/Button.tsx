import classNames from "classnames";
import * as React from "react";

const Button: React.FC<
  {
    variant: "primary" | "outline" | "outline-succeed";
    size: "sm" | "md" | "lg";
    fullWidth?: boolean;
  } & React.DetailedHTMLProps<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    HTMLButtonElement
  >
> = ({ className, fullWidth, variant, size, ...props }) => {
  return (
    <button
      className={classNames(
        "inline-block rounded-md font-medium transition-colors hover:transition-none",
        {
          "w-full": fullWidth,
          "h-[21px] px-2 pt-[1px] pb-[3px] text-xs": size === "sm",
          "h-[36px] px-3 pt-[1px] pb-[3px] text-base": size === "md",
          "h-[44px] px-3 pt-[10px] pb-[13px]": size === "lg",
          "border border-transparent bg-black text-white hover:bg-zinc-700 disabled:bg-zinc-400 disabled:hover:bg-zinc-400":
            variant === "primary",
          "border border-zinc-200 shadow-xs hover:bg-zinc-100 disabled:text-zinc-400 disabled:shadow-none disabled:hover:bg-white":
            variant === "outline",
          "border border-green-400 text-green-600 shadow-xs hover:bg-green-50 disabled:border-green-200 disabled:text-green-200 disabled:shadow-none disabled:hover:bg-white":
            variant === "outline-succeed",
        },
        className,
      )}
      type="button"
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
    />
  );
};

export default Button;
