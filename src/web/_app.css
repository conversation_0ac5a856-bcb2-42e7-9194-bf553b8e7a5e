@import "tailwindcss";

@theme {
  --breakpoint-*: initial;
  --breakpoint-xl: 1280px;
  --font-sans: "IBM Plex Sans", sans-serif;
  --text-sm--line-height: 1.15rem;
  --text-base--line-height: 1.3125rem;
  --text-lg--line-height: 1.4375rem;
}

@layer base {
  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-Regular.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-Regular.ttf") format("truetype");
    font-weight: 400;
  }

  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-RegularItalic.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-RegularItalic.ttf") format("truetype");
    font-weight: 400;
    font-style: italic;
  }

  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-Medium.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-Medium.ttf") format("truetype");
    font-weight: 500;
  }

  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-MediumItalic.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-MediumItalic.ttf") format("truetype");
    font-weight: 500;
    font-style: italic;
  }

  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-SemiBold.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-SemiBold.ttf") format("truetype");
    font-weight: 600;
  }

  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-SemiBoldItalic.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-SemiBoldItalic.ttf") format("truetype");
    font-weight: 600;
    font-style: italic;
  }

  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-Bold.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-Bold.ttf") format("truetype");
    font-weight: bold;
  }

  @font-face {
    font-family: "IBM Plex Sans";
    src:
      url("/fonts/IBMPlexSans-BoldItalic.woff2") format("woff2"),
      url("/fonts/IBMPlexSans-BoldItalic.ttf") format("truetype");
    font-weight: bold;
    font-style: italic;
  }

  html {
    @apply font-sans text-base font-medium text-zinc-900;
  }

  button:focus,
  input:focus,
  select:focus,
  textarea:focus {
    @apply outline-hidden;
  }

  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }

  mark {
    color: inherit;
  }

  select {
    background-color: inherit;
  }
}
