import * as React from "react";

import { useAuthentication } from "../components/Authentication.js";

const SignInPage: React.FC = () => {
  const authentication = useAuthentication();

  React.useEffect(() => {
    if (authentication.state === "authenticated") {
      window.location.replace("/");
    }
  }, [authentication.state]);

  if (authentication.state === "authenticated") {
    return null;
  }

  return (
    <main className="mx-auto mt-8 mb-8 w-[683px] text-lg antialiased">
      <a href="/">
        <svg
          width="130"
          height="41"
          viewBox="0 0 130 41"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="inline-block h-[20px] w-[64px] text-indigo-600"
        >
          <path
            d="M0.164062 7.67969C1.69531 5.21875 3.68229 3.36849 6.125 2.12891C8.54948 0.907552 11.2109 0.296875 14.1094 0.296875C16.388 0.296875 18.457 0.74349 20.3164 1.63672C22.2122 2.54818 23.7344 3.86068 24.8828 5.57422C26.013 7.34245 26.5781 9.375 26.5781 11.6719C26.5781 13.276 26.2865 14.6797 25.7031 15.8828C25.138 17.0312 24.1992 18.1797 22.8867 19.3281C23.9987 19.9479 24.9648 20.7227 25.7852 21.6523C26.5872 22.6003 27.2161 23.6667 27.6719 24.8516C28.1094 26.0182 28.3281 27.2214 28.3281 28.4609C28.3281 30.6302 27.918 32.4987 27.0977 34.0664C26.2227 35.6706 25.0833 36.9648 23.6797 37.9492C22.1849 38.9883 20.5716 39.7357 18.8398 40.1914C17.0352 40.6654 15.1758 40.9023 13.2617 40.9023C11.6029 40.9023 9.98047 40.6471 8.39453 40.1367C6.77214 39.6263 5.23177 38.888 3.77344 37.9219C2.27865 36.9375 1.02083 35.8529 0 34.668L6.31641 28.2422C7.3737 29.6094 8.41276 30.6029 9.43359 31.2227C10.4727 31.8789 11.7305 32.207 13.207 32.207C14.7565 32.207 16.0326 31.8151 17.0352 31.0312C18.0378 30.2474 18.5391 29.0534 18.5391 27.4492C18.5391 25.8451 17.9922 24.7695 16.8984 24.2227C15.7865 23.694 14.2917 23.4297 12.4141 23.4297H10.8828C10.7188 23.4479 10.4727 23.457 10.1445 23.457H9.73438L9.29688 23.4844V16.7031C9.66146 16.7031 10.1081 16.694 10.6367 16.6758H11.3477L12.0859 16.6484C13.0703 16.612 13.9727 16.5026 14.793 16.3203C15.5951 16.138 16.2695 15.7461 16.8164 15.1445C17.3268 14.5794 17.582 13.7409 17.582 12.6289C17.582 11.1341 17.1445 10.0221 16.2695 9.29297C15.3763 8.5638 14.2279 8.19922 12.8242 8.19922C12.168 8.19922 11.5755 8.29036 11.0469 8.47266C10.5911 8.63672 10.1172 8.86458 9.625 9.15625C9.26042 9.41146 8.76823 9.8125 8.14844 10.3594C8.02083 10.4688 7.88411 10.5872 7.73828 10.7148C7.64714 10.806 7.52865 10.9154 7.38281 11.043C7.09115 11.2982 6.89974 11.4714 6.80859 11.5625L0.164062 7.67969ZM29.6953 39.9727H29.7227V40H45.9102C53.8581 40 58.0417 35.9714 58.4609 27.9141V27.4219C58.0417 19.3646 53.8581 15.3359 45.9102 15.3359H39.2109V1.25391H39.0742V1.19922L29.6953 1.14453V39.9727ZM39.2109 24.1133H45.2266C48.0156 24.1133 49.4102 25.3073 49.4102 27.6953C49.4102 30.0651 48.0156 31.25 45.2266 31.25H39.2109V24.1133ZM60.8398 1.19922H70.3555V40L60.8398 39.9727V1.19922ZM71.75 40V1.19922H83.3438C85.9323 1.19922 88.138 1.39974 89.9609 1.80078C91.8203 2.20182 93.4245 2.8763 94.7734 3.82422C96.1224 4.77214 97.1615 6.05729 97.8906 7.67969C98.6198 9.30208 98.9844 11.2799 98.9844 13.6133C98.9844 15.5273 98.6198 17.2409 97.8906 18.7539C97.1615 20.2669 96.1497 21.5612 94.8555 22.6367C93.543 23.7305 92.0026 24.5599 90.2344 25.125C88.4297 25.7083 86.5612 26 84.6289 26H81.293V40H71.75ZM81.293 9.42969V17.7695H84.0273C85.7773 17.7695 87.1263 17.4049 88.0742 16.6758C89.0039 15.9648 89.4688 14.9258 89.4688 13.5586C89.4688 12.2279 89.0039 11.207 88.0742 10.4961C87.1263 9.78516 85.7773 9.42969 84.0273 9.42969H81.293ZM100.352 39.9727H100.379V40H116.566C124.514 40 128.698 35.9714 129.117 27.9141V27.4219C128.698 19.3646 124.514 15.3359 116.566 15.3359H109.867V1.25391H109.73V1.19922L100.352 1.14453V39.9727ZM109.867 24.1133H115.883C118.672 24.1133 120.066 25.3073 120.066 27.6953C120.066 30.0651 118.672 31.25 115.883 31.25H109.867V24.1133Z"
            fill="currentColor"
          />
        </svg>
      </a>

      <h1 className="mt-14 inline-block text-7xl font-bold tracking-tight">
        Матрица имеет тебя
      </h1>
      <p className="mt-4 w-[600px]">
        Все приготовления завершены, чтобы пустить тебя внутрь Зырь. Осталось
        дело за малым — нажать на кнопку “Войти”.
      </p>
      <br />
      <p className="w-[600px]">
        Никаких паролей придумывать и запоминать не нужно. Браузер создаст для
        тебя сверхсекретный ключ и сам запомнит его — с его помощью ты сможешь
        входить внутрь Зырь.
      </p>
      <br />
      <p className="w-[600px]">
        Нажимая кнопку “Войти”, ты соглашаешься, чтобы Зырь использовал куки,
        чтобы запомнить тебя. Ну ты знаешь, как на каждом первом сайте такие
        бесячие плашки есть про куки. Никакие личные данные на Зырь не хранятся
        и третьим лицам ничего не передается.
      </p>

      <button
        className="mt-8 rounded-xl bg-black px-10 py-5 text-xl leading-none font-bold text-white transition-colors hover:bg-zinc-700 hover:transition-none"
        onClick={() => authentication.register()}
        type="button"
      >
        Войти
      </button>
    </main>
  );
};

export default SignInPage;
