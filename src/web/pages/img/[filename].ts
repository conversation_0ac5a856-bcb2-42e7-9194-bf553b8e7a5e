import assert from "node:assert";
import fs from "node:fs";
import http from "node:http";
import path from "node:path";

export async function GET(
  req: http.IncomingMessage,
  res: http.ServerResponse,
): Promise<void> {
  assert(process.env.IMAGES_PATH, "Missing IMAGES_PATH env variable");

  const filename = req.url!.replace(/^\/img\//, "");
  const filepath = path.join(process.env.IMAGES_PATH, filename);

  assert(filepath.startsWith(process.env.IMAGES_PATH), "Malformed path");

  const exists = await fs.promises
    .access(filepath, fs.constants.F_OK)
    .then(() => true)
    .catch(() => false);

  if (!exists) {
    res.statusCode = 404;
    res.end();
    return;
  }

  fs.createReadStream(filepath).pipe(res);
}
