import http from "node:http";

import { GraphQlContext, yogaInstance } from "../dependencies.js";

export async function GET(
  req: http.IncomingMessage,
  res: http.ServerResponse,
): Promise<void> {
  return POST(req, res);
}

export async function POST(
  req: http.IncomingMessage,
  res: http.ServerResponse,
): Promise<void> {
  const context: GraphQlContext = { req, res, ssr: false };
  await yogaInstance.handle(req, res, context);
}
