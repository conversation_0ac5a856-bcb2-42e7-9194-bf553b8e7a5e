import * as React from "react";

import Layout from "../_layout.js";
import { useAuthentication } from "../components/Authentication.js";
import Button from "../components/Button.js";
import Head from "../components/Head.js";
import MovieLargeSnippet from "../components/MovieLargeSnippet.js";
import MovieSmallSnippet from "../components/MovieSmallSnippet.js";
import { useRouter } from "../components/Router.js";
import SegmentedControl from "../components/SegmentedControl.js";
import { graphql, useQuery } from "../graphql/client.js";

const GetFeed = graphql(/* GraphQL */ `
  query GetFeed {
    feed {
      ... on UserMark {
        date
        mark
        movie {
          id
          ...MovieLargeSnippet_MovieFragment
        }
        user {
          id
          name
        }
      }
    }
    genres {
      id
      label
      slug
    }
    randomGenres: genres(sort: RANDOM, limit: 2) {
      id
      label
      slug
      movies(viewed: false, sort: RANDOM, limit: 5) {
        id
        ...MovieSmallSnippet_MovieFragment
      }
    }
  }
`);

export const FeedPage: React.FC = () => {
  const authentication = useAuthentication();
  const router = useRouter();

  const { data } = useQuery(GetFeed);

  const [kinopoiskUrl, setKinopoiskUrl] = React.useState("");
  const [name, setName] = React.useState("");
  const [email, setEmail] = React.useState("");
  const [sent, setSent] = React.useState(false);

  return (
    <main className="container mx-auto px-5 antialiased">
      <Head>
        <title>
          {authentication.state === "unauthenticated"
            ? "Зырь. Находит 💎 кино по твоим интересам"
            : "Зырь"}
        </title>
      </Head>

      {authentication.state === "unauthenticated" ? (
        <aside className="container mx-auto mt-6 rounded-xl bg-zinc-100 px-5 py-8 text-lg">
          <div className="mx-auto mb-3 max-w-prose">
            <h1 className="text-4xl font-bold">
              Находит 💎 кино по твоим интересам
            </h1>
          </div>

          <p className="mx-auto mb-3 max-w-prose">
            Зырь использует твои оценки, чтобы находить людей, которые ставят
            похожие оценки. Затем он использует оценки этих людей, чтобы
            подыскивать тебе классные фильмы, которые ты не найдёшь никаким
            другим способом.
          </p>

          <p className="mx-auto mb-3 max-w-prose">
            Требуется аккаунт на КиноПоиске с как минимум 50 оценками.
          </p>

          <div className="mx-auto max-w-prose">
            <form
              onSubmit={(event) => {
                event.preventDefault();
                authentication.invite({
                  email,
                  kinopoiskUrl,
                  name,
                });
                setSent(true);
              }}
            >
              <table>
                <tr>
                  <td>
                    <label htmlFor="kinopoisk">Профиль на КиноПоиске:</label>
                  </td>
                  <td>
                    <input
                      id="kinopoisk"
                      type="url"
                      placeholder="https://www.kinopoisk.ru/user/789114/"
                      value={kinopoiskUrl}
                      onChange={(event) => setKinopoiskUrl(event.target.value)}
                      required
                      className="ml-4 block min-w-[360px] rounded border border-zinc-200 bg-white px-2 py-0.5 focus:ring-2 focus:ring-indigo-500"
                    />
                  </td>
                </tr>
                <tr>
                  <td className="pt-1">
                    <label htmlFor="name">Имя:</label>
                  </td>
                  <td className="pt-1">
                    <input
                      id="name"
                      type="text"
                      placeholder="Константин Констанинопольский"
                      value={name}
                      onChange={(event) => setName(event.target.value)}
                      required
                      className="ml-4 block min-w-[310px] rounded border border-zinc-200 bg-white px-2 py-0.5 focus:ring-2 focus:ring-indigo-500"
                    />
                  </td>
                </tr>
                <tr>
                  <td className="pt-1">
                    <label htmlFor="email">Имейл для инвайта:</label>
                  </td>
                  <td className="pt-1">
                    <input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(event) => setEmail(event.target.value)}
                      required
                      className="ml-4 block rounded border border-zinc-200 bg-white px-2 py-0.5 focus:ring-2 focus:ring-indigo-500"
                    />
                  </td>
                </tr>
              </table>

              <div className="mt-3">
                {sent ? (
                  <span className="text-green-600">
                    Заявка принята. Ссылка на вход придёт по email в течение
                    суток.
                  </span>
                ) : (
                  <Button variant="primary" size="md" type="submit">
                    Запросить инвайт
                  </Button>
                )}
              </div>
            </form>
          </div>
        </aside>
      ) : null}

      <div className="mt-6 text-xl">
        Жанр:
        <SegmentedControl
          className="ml-1.5"
          items={[
            ...(data?.genres ?? []).map((g) => ({
              label: g.label,
              selected: false,
              href: router.stringify("/movies/genre/:genre", {
                genre: g.slug,
              }),
            })),
          ]}
        />
      </div>

      {data?.randomGenres.map((genre) => (
        <React.Fragment key={genre.id}>
          <h2 className="mt-5 text-3xl font-bold tracking-tight capitalize">
            {genre.label}
          </h2>
          <div className="mt-2 mb-5 grid grid-cols-5 gap-4">
            {genre.movies.map((movie) => (
              <MovieSmallSnippet key={movie.id} movie={movie} />
            ))}
          </div>
        </React.Fragment>
      ))}

      <h2 className="mt-5 text-3xl font-bold tracking-tight">Друзья смотрят</h2>
      <div className="mt-2 mb-10 grid grid-cols-2 gap-4">
        {data?.feed.map(({ movie, date, mark, user }) => (
          <MovieLargeSnippet
            key={date + user.id + movie.id}
            movie={movie}
            aux={{ user, mark, type: "friend" }}
          />
        ))}
      </div>
    </main>
  );
};

const IndexPage: React.FC = () => {
  return (
    <Layout>
      <FeedPage />
    </Layout>
  );
};

export default IndexPage;
