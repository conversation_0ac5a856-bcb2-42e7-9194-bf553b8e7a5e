import * as React from "react";

import Head from "../../../components/Head.js";
import MovieLargeSnippet from "../../../components/MovieLargeSnippet.js";
import { Rewrite, useRouter } from "../../../components/Router.js";
import SegmentedControl from "../../../components/SegmentedControl.js";
import { graphql, MovieSort, useQuery } from "../../../graphql/client.js";

const PersonPageGetPerson = graphql(/* GraphQL */ `
  query PersonPageGetPerson($personSlug: String!, $movieSort: MovieSort!) {
    person(idOrSlug: $personSlug) {
      fullName
      slug
      images {
        sizes {
          height
          url
          width
        }
      }
      movies(sort: $movieSort) {
        id
        ...MovieLargeSnippet_MovieFragment
      }
    }
  }
`);

const PersonPage: React.FC = () => {
  const router = useRouter();

  if (router.pattern !== "/people/:personSlug") {
    throw new Error("Unexpected pattern");
  }

  const sort =
    router.url.searchParams.get("sort") === "reverse-chronological"
      ? MovieSort.ReverseChronological
      : MovieSort.BestFirst;
  const { data } = useQuery(
    PersonPageGetPerson,
    {
      personSlug: router.params.personSlug,
      movieSort: sort,
    },
    { keepPreviousData: true },
  );
  const person = data?.person;

  if (!data) {
    return null;
  }

  if (!person) {
    return <Rewrite to={{ pathname: "/404" }} />;
  }

  return (
    <main className="container mx-auto px-5 antialiased">
      <Head>
        <title>{person.fullName} – Зырь</title>
        <link
          rel="canonical"
          href={new URL(
            router.stringify("/people/:personSlug", {
              personSlug: person.slug,
            }),
            router.url.origin,
          ).toString()}
        />
      </Head>
      <h1 className="mt-2 mb-4 text-7xl font-bold tracking-tight">
        {person.images.length > 0 ? (
          <img
            className="relative top-[-7px] mr-3 inline-block h-[60px] w-[60px] rounded-full"
            alt={person.fullName}
            src={person.images[0].sizes[0].url}
            width={person.images[0].sizes[0].width}
            height={person.images[0].sizes[0].height}
          />
        ) : null}
        {person.fullName}
      </h1>
      <nav className="max-w-[720px] text-lg">
        <div>
          Фильмография:
          <SegmentedControl
            className="ml-1.5"
            items={[
              {
                label: "сначала лучшие",
                href:
                  sort === MovieSort.BestFirst
                    ? undefined
                    : router.url.pathname,
                selected: sort === MovieSort.BestFirst,
              },
              {
                label: "сначала недавние",
                href:
                  sort === MovieSort.ReverseChronological
                    ? undefined
                    : `${router.url.pathname}?sort=reverse-chronological`,
                selected: sort === MovieSort.ReverseChronological,
              },
            ]}
          />
        </div>
      </nav>
      <div className="mt-10 mb-10 grid grid-cols-2 gap-6">
        {person.movies.map((movie) => (
          <MovieLargeSnippet
            key={movie.id}
            movie={movie}
            personSlug={person.slug}
          />
        ))}
      </div>
    </main>
  );
};

export default PersonPage;
