<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <script type="module">
      import { QueryClient } from "@tanstack/react-query";
      import { defaultFetcher } from "/graphql/client";
      import * as React from "react";
      import * as ReactDom from "react-dom/client";

      import App from "/_app";

      window.addEventListener("DOMContentLoaded", () => {
        const dehydratedState = window.__REACT_QUERY_STATE__;

        ReactDom.hydrateRoot(
          window.document.getElementById("zyr-react-root"),
          React.createElement(App, {
            initialURL: window.__ROUTER_STATE__
              ? new URL(window.__ROUTER_STATE__.url)
              : new URL(window.location.href),
            fetcher: defaultFetcher,
            queryClient: new QueryClient(),
            dehydratedState,
          }),
        );
      });
    </script>
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/public/apple-touch-icon.png"
    />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/public/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/public/favicon-16x16.png"
    />
    <link
      rel="mask-icon"
      href="/public/safari-pinned-tab.svg"
      color="#4f46e5"
    />
    <link rel="stylesheet" href="/_app.css" />
    <!--metadata-outlet-->
  </head>
  <body>
    <div id="zyr-react-root"><!--ssr-outlet--></div>
    <!--react-query-state-->
  </body>
</html>
