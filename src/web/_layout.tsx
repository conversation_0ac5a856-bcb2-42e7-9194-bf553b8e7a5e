import * as React from "react";

import Header from "./components/Header.js";
import Search from "./components/Search.js";

// eslint-disable-next-line @typescript-eslint/ban-types
const Layout: React.FC<React.PropsWithChildren<{}>> = (props) => {
  return (
    <>
      <Header />
      <div className="border-b border-zinc-200" />
      <Search />
      {props.children}
    </>
  );
};

export default Layout;
