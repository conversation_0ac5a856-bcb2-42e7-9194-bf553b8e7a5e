/* eslint-disable no-param-reassign */
import {
  Hydrate,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import * as React from "react";

import Layout from "./_layout.js";
import { AuthenticationProvider } from "./components/Authentication.js";
import { HeadState, HeadStateProvider } from "./components/Head.js";
import Router, { MatchResult } from "./components/Router.js";
import { SearchProvider } from "./components/Search.js";
import { GraphQlFetcher, GraphQlFetcherContext } from "./graphql/client.js";
import ProfilePageByDecades from "./pages/[user]/decades.js";
import ProfilePageByDirectors from "./pages/[user]/directors.js";
import FeedPage from "./pages/[user]/feed.js";
import ProfilePageByGenres from "./pages/[user]/genres.js";
import ProfilePage from "./pages/[user]/index.js";
import ProfileTopPage from "./pages/[user]/top.js";
import Page404 from "./pages/404.js";
import IndexPage from "./pages/index.js";
import MoviePage from "./pages/movies/[movieSlug].js";
import MovieByKinopoiskId from "./pages/movies/by-kinopoisk-id/[kinopoiskId].js";
import MoviesGenrePage from "./pages/movies/genre/[genre].js";
import PersonPage from "./pages/people/[personSlug]/index.js";
import PersonMoviePage from "./pages/people/[personSlug]/movies/[movieSlug].js";
import PersonMovieByKinopoiskIdPage from "./pages/people/[personSlug]/movies/by-kinopoisk-id/[kinopoiskId].js";
import SignInPage from "./pages/sign-in.js";
import TopPage from "./pages/top.js";

interface Props {
  initialURL: URL;
  onURLChange?: (url: URL, match: MatchResult) => void;
  fetcher: GraphQlFetcher;
  queryClient: QueryClient;
  dehydratedState?: unknown;
  headState?: HeadState;
}

declare module "./components/Router.js" {
  interface PatternParamsMap {
    "/movies/by-kinopoisk-id/:kinopoiskId": { kinopoiskId: string };
    "/movies/genre/:genre": { genre: string };
    "/movies/:movieSlug": { movieSlug: string };
    "/people/:personSlug/movies/by-kinopoisk-id/:kinopoiskId": {
      kinopoiskId: string;
      personSlug: string;
    };
    "/people/:personSlug/movies/:movieSlug": {
      movieSlug: string;
      personSlug: string;
    };
    "/people/:personSlug": { personSlug: string };
    "/sign-in": Record<string, never>;
    "/top": Record<string, never>;
    "/404": Record<string, never>;
    "/:user/decades": { user: string };
    "/:user/directors": { user: string };
    "/:user/feed": { user: string };
    "/:user/genres": { user: string };
    "/:user/top": { user: string };
    "/:user": { user: string };
    "/": Record<string, never>;
    "*": Record<string, never>;
  }
}

const App: React.FC<Props> = (props) => {
  return (
    <Router
      initialURL={props.initialURL}
      onURLChange={props.onURLChange}
      routes={[
        ["/movies/by-kinopoisk-id/:kinopoiskId", MovieByKinopoiskId],
        ["/movies/genre/:genre", MoviesGenrePage],
        ["/movies/:movieSlug", MoviePage],
        [
          "/people/:personSlug/movies/by-kinopoisk-id/:kinopoiskId",
          PersonMovieByKinopoiskIdPage,
        ],
        ["/people/:personSlug/movies/:movieSlug", PersonMoviePage],
        ["/people/:personSlug", PersonPage],
        ["/sign-in", SignInPage],
        ["/top", TopPage],
        ["/404", Page404],
        ["/:user/decades", ProfilePageByDecades],
        ["/:user/directors", ProfilePageByDirectors],
        ["/:user/feed", FeedPage],
        ["/:user/genres", ProfilePageByGenres],
        ["/:user/top", ProfileTopPage],
        ["/:user", ProfilePage],
        ["/", IndexPage],
        ["*", Page404],
      ]}
    >
      {(context) => (
        <GraphQlFetcherContext.Provider value={props.fetcher}>
          <QueryClientProvider client={props.queryClient}>
            <Hydrate state={props.dehydratedState}>
              <HeadStateProvider value={props.headState ?? null}>
                <AuthenticationProvider>
                  <SearchProvider>
                    {context.pattern === "/sign-in" ||
                    context.pattern === "/" ||
                    context.pattern === "*" ||
                    context.pattern === "/404" ? (
                      <context.Component />
                    ) : (
                      <Layout>
                        <context.Component />
                      </Layout>
                    )}
                  </SearchProvider>
                </AuthenticationProvider>
              </HeadStateProvider>
            </Hydrate>
          </QueryClientProvider>
        </GraphQlFetcherContext.Provider>
      )}
    </Router>
  );
};

export default App;
