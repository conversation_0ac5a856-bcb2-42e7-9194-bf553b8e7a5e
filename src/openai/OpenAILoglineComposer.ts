import assert from "assert";
import OpenAI from "openai";

import {
  ComposeLoglineResult,
  LoglineComposeError,
  LoglineComposer,
} from "../jobs/ComposeLoglinesJob.js";

export default class OpenAILoglineComposer implements LoglineComposer {
  constructor(
    private client: OpenAI,
    private model: string,
  ) {}

  async composeLogline(plot: string): Promise<ComposeLoglineResult> {
    const completion = await this.client.chat.completions.create({
      model: this.model,
      messages: [
        {
          role: "system",
          content: `
              Ты редактор сайта о кино. Тебе будет предоставлен сюжет фильма. Составь короткий (до 12 слов) однопредложный премиз на русском языке для фильма. Премиз не должен содержать спойлеров, то есть упоминания того, как кончается история.
    
              Примеры:
              "Три солдата пытаются найти себя в обществе после Второй мировой войны"
              "Сын главы мафиозного клана оказывается втянутым в мир преступности против своей воли"
              "Мальчик пытается найти свою мать в преступном мире"
    
              Выведи только короткий (до 12 слов) однопредложный премиз.
            `,
        },
        {
          role: "user",
          content: plot,
        },
      ],
    });

    let premise = completion.choices[0].message.content!;

    assert(
      typeof premise === "string",
      new LoglineComposeError(
        `LLM response doesn't match to shape`,
        completion.choices[0].message.content!,
      ),
    );

    premise = premise
      .replace(/\.$/, "")
      .replace(/^"/, "")
      .replace(/"$/, "")
      .replace(/\.$/, "");

    return {
      logline: premise,
      model: this.model,
      prompt: "",
      system: "",
      options: {},
    };
  }
}
