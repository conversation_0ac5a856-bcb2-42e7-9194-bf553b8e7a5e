import * as fs from "node:fs";
import * as path from "node:path";
import got from "got";

export default class TelegramChannelReadeer {
  constructor(
    private slug: string,
    private cacheDirpath: string,
  ) {}

  async readMessages(): Promise<Message[]> {
    const startDate = new Date();
    const cachedMessages = await this.readCachedMessages();
    const ids = new Set<number>();

    for (const message of cachedMessages) {
      ids.add(message.id);
    }

    const messages: Message[] = [];
    let page = 1;
    let gotAnythingNew = false;

    do {
      gotAnythingNew = false;

      const body = await this.fetchMessages({
        slug: this.slug,
        page,
      });

      for (const message of body.messages) {
        if (!ids.has(message.id)) {
          messages.push({
            id: message.id,
            date: new Date(message.date * 1000),
            message:
              message.message
                ?.replace(/<br \/>/g, "\n")
                .replace(/<br>/g, "\n") ?? null,
          });
          gotAnythingNew = true;
        }

        ids.add(message.id);
      }

      if (gotAnythingNew && "messages" in body) {
        await fs.promises.writeFile(
          path.join(
            this.cacheDirpath,
            startDate.toISOString() + "-" + (1000 - page),
          ),
          JSON.stringify(body, null, 2),
        );
      }

      page += 1;
    } while (gotAnythingNew);

    return messages.concat(cachedMessages);
  }

  private async fetchMessages(params: {
    slug: string;
    page: number;
  }): Promise<SuccessResponse> {
    let retries = 3;
    let body: Response;

    do {
      const response = await got(
        `https://tg.i-c-a.su/json/${params.slug}?limit=100&page=${params.page}`,
        {
          method: "GET",
          headers: {},
          throwHttpErrors: false,
          timeout: 5_000,
          retry: 1,
        },
      );
      body = JSON.parse(response.body) as Response;

      if ("messages" in body) {
        return body;
      }

      const rawFloodWait = body.errors
        .find((error) => error.startsWith("FLOOD_WAIT_"))
        ?.replace("FLOOD_WAIT_", "");
      const floodWait =
        typeof rawFloodWait === "string" ? Number(rawFloodWait) : null;

      if (floodWait != null) {
        await new Promise((resolve) => setTimeout(resolve, floodWait * 1000));
      }

      retries -= 1;
    } while (retries > 0);

    throw new TypeError(
      "Expected valid response, instead got: " + JSON.stringify(body),
    );
  }

  private async readCachedMessages(): Promise<Message[]> {
    const messages: Message[] = [];
    const ids = new Set<number>();
    let filenames = await fs.promises.readdir(this.cacheDirpath);

    filenames = filenames.sort((a, b) => b.localeCompare(a));

    for (const filename of filenames) {
      const file = await fs.promises.readFile(
        path.join(this.cacheDirpath, filename),
        "utf-8",
      );
      const json = JSON.parse(file) as SuccessResponse;

      for (const message of json.messages) {
        if (!ids.has(message.id)) {
          messages.push({
            id: message.id,
            date: new Date(message.date * 1000),
            message:
              message.message
                ?.replace(/<br \/>/g, "\n")
                .replace(/<br>/g, "\n") ?? null,
          });
        }

        ids.add(message.id);
      }
    }

    return messages;
  }
}

export interface Message {
  id: number;
  date: Date;
  message: string | null;
}

type Response = SuccessResponse | FailedResponse;

interface SuccessResponse {
  messages: {
    id: number;
    date: number;
    message: string | null;
  }[];
}

interface FailedResponse {
  errors: string[];
}
