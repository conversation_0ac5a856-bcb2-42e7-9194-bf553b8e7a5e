import * as cssSelect from "css-select";
import * as domhandler from "domhandler";
import * as domutils from "domutils";
import got from "got";
import * as htmlparser2 from "htmlparser2";

import {
  WikimediaGateway,
  WikipediaArticle,
  WikipediaArticleRef,
} from "../jobs/PullWikipediaArticlesJob.js";

interface ConstructorOptions {
  accessToken?: string;
}

export default class RestWikimediaGateway implements WikimediaGateway {
  private accessToken?: string;

  constructor(options?: ConstructorOptions) {
    this.accessToken = options?.accessToken;
  }

  async getWikipediaArticle(
    ref: WikipediaArticleRef,
    ifModifiedSince?: Date,
  ): Promise<WikipediaArticle | null> {
    const url = `https://api.wikimedia.org/core/v1/wikipedia/${
      ref.lang
    }/page/${encodeURIComponent(ref.slug)}/html`;
    const response = await got(url, {
      method: "GET",
      headers: {
        Authorization: this.accessToken
          ? `Bearer ${this.accessToken}`
          : undefined,
        "If-Modified-Since": ifModifiedSince
          ? ifModifiedSince.toUTCString()
          : undefined,
      },
      throwHttpErrors: false,
      timeout: 5_000,
      retry: 1,
    });

    if (!response) {
      return null;
    }

    if (response.statusCode === 404 || response.statusCode === 304) {
      return null;
    }

    if (response.statusCode !== 200) {
      throw new Error(
        `${
          response.statusMessage
            ? `${response.statusCode} ${response.statusMessage}`
            : response.statusCode
        } from ${url}`,
      );
    }

    return {
      html: response.body,
      plot: this.parsePlotFromHtml(response.body),
      genres: await this.parseGenresFromHtml(response.body, ref.lang),
    };
  }

  // eslint-disable-next-line class-methods-use-this
  parsePlotFromHtml(html: string): string | null {
    const document = htmlparser2.parseDocument(html);
    const heading =
      cssSelect.selectOne("h2#Plot", document) ??
      cssSelect.selectOne("h2#Сюжет", document);

    if (!heading) {
      return null;
    }

    const siblings = domutils.getSiblings(heading);
    const paragraphs = siblings.slice(1);

    let plot = "";

    for (const paragraph of paragraphs) {
      if (
        paragraph instanceof domhandler.Element &&
        paragraph.tagName === "p"
      ) {
        plot += domutils.textContent(paragraph);
      } else if (paragraph instanceof domhandler.Text) {
        plot += domutils.textContent(paragraph);
      }
    }

    plot = plot.trim();

    return plot === "" ? null : plot;
  }

  async parseGenresFromHtml(
    html: string,
    lang: "en" | "ru",
  ): Promise<{ lang: "en" | "ru"; slug: string }[]> {
    const document = htmlparser2.parseDocument(html);
    const genreAnchors = cssSelect.selectAll<
      domhandler.Document,
      domhandler.Element
    >("[data-wikidata-property-id=P136] a", document);

    const genreSlugs = genreAnchors
      .map((genre) =>
        domutils.getAttributeValue(genre, "href")!.replace("./", ""),
      )
      .filter((slug) => !slug.includes("cite_note") && slug.length < 50);

    // Resolve redirects for the genre slugs
    const redirects = await this.resolveWikipediaRedirects(genreSlugs, lang);
    const slugRedirects = new Map<string, string>();
    redirects.forEach(({ originalSlug, resolvedSlug }) => {
      slugRedirects.set(originalSlug, resolvedSlug);
    });

    // Apply redirects and deduplicate
    const resolvedSlugs = Array.from(
      new Set(genreSlugs.map((slug) => slugRedirects.get(slug) ?? slug)),
    );

    return resolvedSlugs.map((slug) => ({
      lang,
      slug,
    }));
  }

  // eslint-disable-next-line class-methods-use-this
  async resolveWikipediaRedirects(
    slugs: string[],
    language: "en" | "ru" = "ru",
  ): Promise<{ originalSlug: string; resolvedSlug: string }[]> {
    const results: { originalSlug: string; resolvedSlug: string }[] = [];

    for (const chunk of chunks(slugs, 50)) {
      const wikipediaApiUrl =
        language === "en"
          ? "https://en.wikipedia.org/w/api.php"
          : "https://ru.wikipedia.org/w/api.php";

      const response = await got(wikipediaApiUrl, {
        searchParams: {
          action: "query",
          format: "json",
          titles: chunk.join("|"),
          redirects: "1",
        },
        responseType: "json",
        timeout: {
          request: 10000,
        },
      });

      const data = response.body as WikipediaApiResponse;

      if (data.query?.redirects) {
        data.query.redirects.forEach((redirect) => {
          results.push({
            originalSlug: redirect.from.replace(" ", "_"),
            resolvedSlug: redirect.to.replace(" ", "_"),
          });
        });
      }
    }

    return results;
  }
}

function chunks<T>(list: T[], size: number): T[][] {
  const cnks: T[][] = [];
  let chunk: T[] = [];

  list.forEach((item, index) => {
    chunk.push(item);

    if (chunk.length === size || index === list.length - 1) {
      cnks.push(chunk);
      chunk = [];
    }
  });

  return cnks;
}

interface WikipediaApiResponse {
  query?: {
    redirects?: {
      from: string;
      to: string;
    }[];
    pages?: Record<
      string,
      {
        pageid?: number;
        title?: string;
        missing?: boolean;
      }
    >;
  };
}
