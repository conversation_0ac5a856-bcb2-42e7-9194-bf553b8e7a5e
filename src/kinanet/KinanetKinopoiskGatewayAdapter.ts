import * as fs from "node:fs";

import {
  KinopoiskGateway,
  KinopoiskGatewayFetchUserResponse,
  KinopoiskJob,
} from "../jobs/PullKinopoiskMarksJob.js";
import TelegramChannelReader from "../telegram/TelegramChannelReader.js";
import KinanetMovieAssociator from "./KinanetMovieAssociator.js";
import MarksSheetReader from "./MarksSheetReader.js";

export default class KinanetKinopoiskGatewayAdapter
  implements KinopoiskGateway
{
  constructor(
    private marksSheetReader: MarksSheetReader,
    private movieAssociator: KinanetMovieAssociator,
    private kinanetTelegramChannelReader: TelegramChannelReader,
    private nextGateway: KinopoiskGateway,
    private pdfTextFilepaths: string[],
  ) {}

  async fetchUser(
    planItem: KinopoiskJob,
  ): Promise<KinopoiskGatewayFetchUserResponse | null> {
    if (planItem.id !== "3253175") {
      let user = await this.nextGateway.fetchUser(planItem);

      if (user?.id === "789114") {
        user = {
          ...user,
          friends: [...user.friends, { id: "3253175", name: "kinanet" }],
        };
      }

      return user;
    }

    try {
      let kinanetMarks: Mark[] = [];

      const messages = await this.kinanetTelegramChannelReader.readMessages();

      for (const message of messages) {
        if (
          message.message?.startsWith("Только оценки") ||
          message.message?.startsWith("Лучшие фильмы")
        ) {
          kinanetMarks = [
            ...kinanetMarks,
            ...(await this.marksSheetReader.readMarks(message.message)).map(
              (mark): Mark => ({
                directors: mark.directors,
                titles: mark.titles,
                years: mark.years,
                mark: mark.mark,
                timestamp: message.date,
              }),
            ),
          ];
        }
      }

      for (const filepath of this.pdfTextFilepaths) {
        const text = await fs.promises.readFile(filepath, "utf-8");

        kinanetMarks = [
          ...kinanetMarks,
          ...(await this.marksSheetReader.readMarks(text)).map(
            (mark): Mark => ({
              directors: mark.directors,
              titles: mark.titles,
              years: mark.years,
              mark: mark.mark,
              timestamp: mark.years[0]
                ? new Date(`${mark.years[0]}-01-01T12:00:00`)
                : new Date(`1970-01-01T12:00:00`),
            }),
          ),
        ];
      }

      const associatedMovies =
        await this.movieAssociator.associateMovies(kinanetMarks);
      const scrapedAt = new Date();

      return {
        id: planItem.id,
        name: "kinanet",
        friends: [],
        marks: kinanetMarks
          .map(
            (
              mark,
              index,
            ): KinopoiskGatewayFetchUserResponse["marks"][number] | null => {
              const associatedMovie = associatedMovies[index];

              if (associatedMovie?.year == null) {
                return null;
              }

              return {
                movie: {
                  id: associatedMovie.kinopoiskId,
                  title: associatedMovie.titles[0],
                  year: associatedMovie.year,
                },
                score: Math.round(mark.mark),
                timestamp: mark.timestamp,
                scrapedAt,
              };
            },
          )
          .filter(
            (
              mark,
            ): mark is KinopoiskGatewayFetchUserResponse["marks"][number] =>
              mark != null,
          )
          .filter(
            (mark) =>
              !planItem.marks.after || mark.timestamp > planItem.marks.after,
          ),
      };
    } catch (error) {
      if (planItem.marks.after != null) {
        return {
          id: planItem.id,
          name: "kinanet",
          friends: [],
          marks: [],
        };
      }

      throw error;
    }
  }
}

interface Mark {
  directors: string[];
  titles: string[];
  years: number[];
  mark:
    | 0.5
    | 1
    | 1.5
    | 2
    | 2.5
    | 3
    | 3.5
    | 4
    | 4.5
    | 5
    | 5.5
    | 6
    | 6.5
    | 7
    | 7.5
    | 8
    | 8.5
    | 9
    | 9.5
    | 10;
  timestamp: Date;
}
