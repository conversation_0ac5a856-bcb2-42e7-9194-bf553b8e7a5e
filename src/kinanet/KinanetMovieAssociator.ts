/* eslint-disable max-classes-per-file */
import assert from "node:assert";
import MiniSearch from "minisearch";

interface Idiosyncrasies {
  directors: Record<string, string | undefined>;
  titles: Record<string, string | undefined>;
}

const defaultIdiosyncrasies: Idiosyncrasies = {
  directors: {
    "Алехандро Г. Иньярриту": "Алехандро Гонсалес Иньярриту",
    "Апхичатпхонг Вирасетакун": "Апичатпонг Вирасетакул",
    "Дж. Дж. Эйбрамс": "Джей Джей Абрамс",
    "Джереми Пол Кэган": "Джереми Каган",
    "Джессика Хауснер": "Джессика Хаузнер",
    "Дэнни Пан": "Дэнни Пэнг",
    "Мерви<PERSON> Ле Рой": "Мервин Лерой",
    "Мишель Азанависюс": "Мишель Хазанавичус",
    "Пауль Верхувен": "Пол Верховен",
    "Роман Поляньский": "Роман Полански",
    "Тео Ангелопулос": "Теодорос Ангелопулос",
    "Терри Звайгофф": "Терри Цвигофф",
    "Хауард Дойч": "Ховард Дойч",
    "Хауард Сторм": "Ховард Сторм",
    "Хауард Фрэнклин": "Ховард Франклин",
    "Хауард Хокс": "Говард Хоукс",
    "Чарльз Спенсер Чаплин": "Чарли Чаплин",
    "Чарльз Чаплин": "Чарли Чаплин",
    "Эвальд Андре Дюпон": "Эдвард Андрэ Дюпон",
    "Эдвин Стэттон Портер": "Эдвин Портер",
    "Эдуар Ньерман": "Эдуард Ниэрманс",
    "Энди Вачовски": "Лилли Вачовски",
  },
  titles: {
    "...И ВЕТЕР НАС УНЕСЁТ": "Нас унесет ветер",
    "АГАТА И БЕЗОСТАНОВОЧНОЕ ЧТЕНИЕ": "Агата, или Бесконечное чтение",
    "АЛАЯ ПТИЦА НЕЖНОЙ ЛЮБВИ": "Судзаку",
    "АНОНИМНАЯ - ЖЕНЩИНА В БЕРЛИНЕ": "Безымянная – одна женщина в Берлине",
    "АРОМАТ ИВОНН": "Аромат Ивонны",
    "АРОМАТ ЖЕНЩИНЫ В ЧЁРНОМ": "Аромат дамы в черном",
    'ДАНДИ ПО ПРОЗВИЩУ "КРОКОДИЛ"': "Крокодил Данди",
    "АЛЬФАВИЛЬ, СТРАННОЕ ПРИКЛЮЧЕНИЕ ЛЕММИ КОШНА": "Альфавиль",
    "БЕГ ЗАЙЦА ПО ПОЛЯМ": "Бег зайца через поля",
    "БЕЗ ОБЕЗБОЛИВАНИЯ": "Без наркоза",
    "БУДЬ СО МНОЙ": "Останься со мной",
    "БЕЖИМ БЕЗ ОГЛЯДКИ": "Беги без оглядки",
    "БЕЗУМНЫЙ МАКС ЗА ПРЕДЕЛАМИ КУПОЛА ГРОМА":
      "Безумный Макс 3: Под куполом грома",
    "БЕЛАЯ ЛЕНТА - НЕМЕЦКАЯ ДЕТСКАЯ ИСТОРИЯ": "Белая лента",
    'БИЛЛ И ТЕД: ПРИКЛЮЧЕНИЕ НА "ОТЛИЧНО"':
      "Невероятные приключения Билла и Теда",
    "БЛИЗКИЕ КОНТАКТЫ ТРЕТЬЕГО ВИДА": "Близкие контакты третьей степени",
    "БОЛЬНАЯ ДЕВУШКА": "Тошнит от себя",
    "БРАТ СОЛНЦЕ И СЕСТРА ЛУНА": "Брат Солнце, сестра Луна",
    "БОЛЬШОЙ ПЕРЕПОЛОХ В КИТАЙСКОМ КВАРТАЛЕ":
      "Большой переполох в маленьком Китае",
    "БЭРРИ ЛИНДОН": "Барри Линдон",
    "ВЕДЬМЫ ИЗ ИСТВИКА": "Иствикские ведьмы",
    "ВЕЧЕРНИЙ ТУАЛЕТ": "Вечернее платье",
    'ВОСПОМИНАНИЯ В "ЗВЁЗДНОЙ ПЫЛИ"': "Звездные воспоминания",
    "ВЫСОКИЕ МОМЕНТЫ ОДИНОЧЕСТВА": "Глубокое одиночество",
    "ВЫЖИВУТ ТОЛЬКО ВЛЮБЛЁННЫЕ": "Выживут только любовники",
    "ГОЛУБЫЕ ГОРЫ, ИЛИ НЕПРАВДОПОБНАЯ ИСТОРИЯ":
      "Голубые горы, или Неправдоподобная история",
    "ГРОЗА НАД ДОМОМ": "Смерть во французском саду",
    "ГРЯЗНЫЙ ХЭРРИ": "Грязный Гарри",
    "ГУБКА БОБ КВАДРАТНЫЕ ШТАНЫ. ФИЛЬМ": "Губка Боб – квадратные штаны",
    "ДЕВУШКА NO6": "Девушка №6",
    "ДЕМОН, ПОЯВЛЯЮЩИЙСЯ СРЕДЬ БЕЛА ДНЯ": "Дневной демон",
    "ДНЕВНИК СТРАНСТВУЮЩЕЙ": "Бабье царство",
    "ДНИ МОЛОДОСТИ: СТУДЕНЧЕСКИЙ РОМАН": "Дни молодости",
    "ДНИ ШАЛЬНОЙ ЖИЗНИ": "Дикие дни",
    "ДОКТОР ДЖЕКИЛ И МИСТЕР ХАЙД": "Доктор Джекилл и мистер Хайд",
    "ДОРОГАЯ, Я УВЕЛИЧИЛ МАЛЫША": "Дорогая, я увеличил ребенка",
    "ДРУЗЬЯ В КОНФЛИКТЕ - В ЯПОНСКОМ СТИЛЕ": "Друзья в конфликте",
    "ДЬЯВОЛЬСКИЕ ДУШИ": "Дьяволицы",
    "ДОКТОР ХРЕН": "Доктор Пополь",
    "ДЖЕРРИ МАГУАЙР": "Джерри Магуайер",
    "ДЖОННИ ВЫДАЛИ РУЖЬЁ": "Джонни взял ружье",
    "ЖЕНА ПАСТОРА": "Вдова пастора",
    "ЖЕНЩИНА В СИНЕМ": "Женщина в голубом",
    "ЖИВЁШЬ ТОЛЬКО РАЗ": "Живем один раз",
    "ЖИТЬ СВОЕЙ ЖИЗНЬЮ: ФИЛЬМ В ДВЕНАДЦАТИ КАРТИНАХ": "Жить своей жизнью",
    "ЗА НЕСКОЛЬКО ЛИШНИХ ДОЛЛАРОВ": "На несколько долларов больше",
    "ЗАКРОЙ ГЛАЗА": "Закройте глаза",
    "ИДИОТЫ - ДОГМА 2": "Идиоты",
    "ИНДИАНА ДЖОНС И ХРАМ РОКА": "Индиана Джонс и Храм Судьбы",
    "ИНДУСТРИАЛЬНАЯ СИМФОНИЯ NO1. СОН ДЕВУШКИ С РАЗБИТЫМ СЕРДЦЕМ":
      "Индустриальная симфония №1: Сон девушки с разбитым сердцем",
    "ИНТЕРВЬЮ С ВАМПИРОМ: ВАМПИРСКИЕ ХРОНИКИ": "Интервью с вампиром",
    "ИСКАТЕЛИ ПОТЕРЯННОГО КОВЧЕГА":
      "Индиана Джонс: В поисках утраченного ковчега",
    "ИТАЛЬЯНСКИЙ ДЛЯ НАЧИНАЮЩИХ - ДОГМА 12": "Итальянский для начинающих",
    "КАК В ЗЕРКАЛЕ": "Сквозь тёмное стекло",
    "КЛАТБЕЩЕ ДОМАШНИХ ЖИВОТНЫХ II": "Кладбище домашних животных 2",
    "КОРОЛЬ ЖИВ": "Король жив",
    "КОШМАР НА УЛИЦЕ ВЯЗОВ 3: БОРЮЩИЕСЯ ВО СНЕ":
      "Кошмар на улице Вязов 3: Воины сна",
    "КРЁСТНЫЙ ОТЕЦ, ЧАСТЬ II": "Крестный отец 2",
    "КРЁСТНЫЙ ОТЕЦ, ЧАСТЬ III": "Крестный отец 3",
    "КТО ЭТО ТАМ ПОЁТ?": "Кто там поет",
    "ЛИГА СПРАВЕДЛИВОСТИ ЗЭКА СНАЙДЕРА": "Лига справедливости Зака Снайдера",
    "ЛИХОРАДКА В СУББОТУ ВЕЧЕРОМ": "Лихорадка субботнего вечера",
    "ЛОРЕНС АРАВИЙСКИЙ": "Лоуренс Аравийский",
    ЛИХАЧИ: "Гонщики",
    ЛЮБИТЕЛЬ: "Дилетанты",
    "ЛЮБОВНИКИ - ДОГМА 5": "Любовники",
    "МАГАЗИН ЗА УГЛОМ": "Магазинчик за углом",
    "МЕКТУБ, МОЯ ЛЮБОВЬ: ПЕСНЬ ПЕРВАЯ": "Мектуб, моя любовь",
    "МЕСЬЕ КЛЕЙН": "Мсье Кляйн",
    "МОЙ ДЯДЯ": "Мой дядюшка",
    "МОНТЕНЕГРО, ИЛИ СВИНЬИ И БИСЕР": "Монтенегро",
    "МОЯ НОЧЬ У МОД": "Ночь у Мод",
    "МУЖСКОЕ-ЖЕНСКОЕ: 15 ТОЧНЫХ ФАКТОВ": "МУЖСКОЕ-ЖЕНСКОЕ",
    "НАЗАД В БУДУЩЕЕ, ЧАСТЬ II": "Назад в будущее 2",
    "НАЗАД В БУДУЩЕЕ, ЧАСТЬ III": "Назад в будущее 3",
    "НАМЕРТВО СВЯЗАННЫЕ": "Связанные насмерть",
    "НАРОД ПРОТИВ ЛЭРРИ ФЛИНТА": "Народ против Ларри Флинта",
    "НЕЛЛИ И ГОСПОДИН АРНО": "Нелли и месье Арно",
    "НЕПРИЯТНОСТИ С ХЭРРИ": "Неприятности с Гарри",
    "НЕУДАЧНЫЙ ТРАХ, ИЛИ БЕЗУМНОЕ ПОРНО": "Безумное кино для взрослых",
    "ОДЕРЖИМАЯ БЕСОМ": "Одержимая",
    "ПОЛНА КРАСНЫМ РЕКА": "Полноводная красная река",
    "ПОДПОЛЬЕ - ЖИЛА-БЫЛА ОДНА СТРАНА": "Андеграунд",
    "ПОСЛЕДНЕЕ ЛЕТО...": "Запретная страсть",
    "ПОСЛЕДНИЙ ГЕРОЙ БОЕВИКА": "Последний киногерой",
    "ПРИГОТОВЬТЕ ПЛАТОЧКИ": "Приготовьте ваши носовые платки",
    "ПРИНЕСИТЕ МНЕ ГОЛОВУ АЛЬФРЕДО ГАРСИИ":
      "Принесите мне голову Альфредо Гарсиа",
    "ПЭТ ГЭРРЕТ И БИЛЛИ КИД": "Пэт Гэрретт и Билли Кид",
    "РИМ ФЕЛЛИНИ": "Рим",
    "СНЕЖНОЕ СООБЩЕСТВО": "Общество снега",
    "ТАИНСТВЕННЫЙ КЛУБ": "Клуб самоубийц",
    "ТУРЕЦКИЕ СЛАДОСТИ": "Турецкие наслаждения",
    "ТУТСИ (МИЛАШКА)": "Тутси",
    "УБИТЬ БИЛЛА. Т.1": "Убить Билла",
    "УБОГАЯ ЗЕМЛЯ": "Земля бога",
    "ЧУЖОЙ³": "Чужой 3",
    "ЦВЕТ БОГА": "Цвет рая",
    "ЦВЕТОК ТЫСЯЧИ И ОДНОЙ НОЧИ": "Цветок тысяча и одной ночи",
    "ШОУ ТРУМЕНА": "Шоу Трумана",
    "ЭТА ПРЕКРАСНАЯ ЖИЗНЬ": "Эта замечательная жизнь",
    NO10: "Номер десять",
    АННЕТ: "Аннетт",
    БЛЕЙД: "Блэйд",
    БЛУЖДАНИЕ: "Обход",
    ВИТТГЕНШТЕЙН: "Витгенштейн",
    ВТОРИЧНЫЕ: "Второй раз",
    ГАББЕ: "Габбех",
    КОЯНИСКАТСИ: "Кояанискатси",
    МАРЬЯЧИ: "Музыкант",
    НАГИЕ: "Обнаженная",
    НАКОЙКАТСИ: "Накойкаци",
    ПРЕДЗНАМЕНОВАНИЕ: "Омен",
    ПСИХОЗ: "Психо",
    СМУТА: "Ран",
    УРОДЫ: "Уродцы",
  },
};

export interface KinanetMovie {
  directors: string[];
  titles: string[];
  years: number[];
}

export interface Movie {
  kinopoiskId: string;
  titles: string[];
  directors: string[];
  year?: number;
}

interface SearchMovie {
  id: string;
  titles: string[];
  directors: string[];
  year?: number;
}

export interface MovieRepository {
  findAll(): Promise<Movie[]>;
}

export default class KinanetMovieAssociator {
  constructor(private movieRepository: MovieRepository) {}

  async associateMovies(
    kinanetMovies: KinanetMovie[],
  ): Promise<(Movie | null)[]> {
    const movieSearch = new MiniSearch<SearchMovie>({
      fields: ["titles", "directors"],
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
      extractField: (document: any, fieldName) =>
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
        Array.isArray(document[fieldName])
          ? // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
            document[fieldName].join(" ")
          : // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
            document[fieldName],
      processTerm: (term) =>
        term
          .replace(/[-,.:;?!*+’]/g, "")
          .toLowerCase()
          .replace("ё", "е"),
    });
    const movies = await this.movieRepository.findAll();
    const searchMovies = movies.map(
      (movie): SearchMovie => ({
        id: movie.kinopoiskId,
        directors: movie.directors,
        titles: movie.titles,
      }),
    );
    const moviesMap = new Map(
      movies.map((movie) => [movie.kinopoiskId, movie]),
    );
    const searchMoviesMap = new Map(
      searchMovies.map((movie) => [movie.id, movie]),
    );

    movieSearch.addAll(searchMovies);

    const idiosyncrasies = deduceIdiosyncrasies(
      movieSearch,
      moviesMap,
      kinanetMovies,
    );

    return kinanetMovies.map((kinanetMovie): Movie | null => {
      const movie =
        findMovieByTitleAndYearAndDirector(
          movieSearch,
          moviesMap,
          idiosyncrasies,
          kinanetMovie,
        ) ??
        (kinanetMovie.directors.length === 1
          ? findMovieByYearAndDirector(
              movieSearch,
              moviesMap,
              idiosyncrasies,
              kinanetMovie,
            ) ??
            findMovieByTitleAndYearWithoutDirector(
              movieSearch,
              moviesMap,
              idiosyncrasies,
              kinanetMovie,
            )
          : null);

      if (movie != null) {
        const searchMovie = searchMoviesMap.get(movie.kinopoiskId);

        assert(searchMovie, "Movie not found");

        movieSearch.remove(searchMovie);

        return movie;
      }

      return null;
    });
  }
}

function deduceIdiosyncrasies(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  kinanetMovies: KinanetMovie[],
): Idiosyncrasies {
  const idiosyncrasies = {
    directors: { ...defaultIdiosyncrasies.directors },
    titles: { ...defaultIdiosyncrasies.titles },
  };
  const directors = new Map<string, KinanetMovie[]>();

  for (const movie of kinanetMovies) {
    if (movie.directors.length > 1) {
      continue;
    }

    for (const director of movie.directors) {
      if (directors.has(director)) {
        directors.set(director, [...directors.get(director)!, movie]);
      } else {
        directors.set(director, [movie]);
      }
    }
  }

  for (const [director, filmography] of directors) {
    if (director in idiosyncrasies.directors || filmography.length === 0) {
      continue;
    }

    const directorResults = findMoviesByDirector(
      movieSearch,
      moviesMap,
      defaultIdiosyncrasies,
      director,
    );

    if (directorResults.length > 0) {
      continue;
    }

    const deducedDirector =
      filmography.length >= 2
        ? deduceDirectorFromFilmography(movieSearch, moviesMap, filmography) ??
          deduceDirectorFromFuzzyFullName(
            movieSearch,
            moviesMap,
            filmography[0],
          )
        : deduceDirectorFromFuzzyFullName(
            movieSearch,
            moviesMap,
            filmography[0],
          );

    if (deducedDirector != null) {
      idiosyncrasies.directors[director] = deducedDirector;
    }
  }

  return idiosyncrasies;
}

function deduceDirectorFromFilmography(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  filmography: KinanetMovie[],
): string | null {
  assert(
    filmography.length >= 2,
    "Cannot deduce director from filmography having less than 2 movies",
  );

  const suggestions = new Set<string>();

  for (const kinanetMovie of filmography) {
    assert(
      kinanetMovie.directors.length === 1,
      "Cannot deduce director from movie having two or more directors",
    );

    const movie = findMovieByTitleAndYear(
      movieSearch,
      moviesMap,
      defaultIdiosyncrasies,
      kinanetMovie,
    );

    if (movie != null && movie.directors.length === 1) {
      suggestions.add(movie.directors[0]);
    }
  }

  if (suggestions.size === 1) {
    const [resolvedDirector] = suggestions.keys();

    return resolvedDirector;
  }

  return null;
}

function deduceDirectorFromFuzzyFullName(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  kinanetMovie: KinanetMovie,
): string | null {
  const movie = findMovieByTitleAndYearAndFuzzyDirector(
    movieSearch,
    moviesMap,
    defaultIdiosyncrasies,
    kinanetMovie,
  );

  return movie != null ? movie.directors[0] : null;
}

function findMovieByTitleAndYearWithoutDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  idiosyncrasies: Idiosyncrasies,
  kinanetMovie: KinanetMovie,
): Movie | null {
  const movie = findMovieByTitleAndYear(
    movieSearch,
    moviesMap,
    idiosyncrasies,
    kinanetMovie,
  );

  return movie?.directors.length === 0 && movie.directors.length === 0
    ? movie
    : null;
}

function findMovieByTitleAndYear(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  idiosyncrasies: Idiosyncrasies,
  kinanetMovie: KinanetMovie,
): Movie | null {
  let movieResults = movieSearch
    .search({
      queries: [
        {
          fields: ["titles"],
          queries: kinanetMovie.titles
            .map((title) => idiosyncrasies.titles[title.toUpperCase()] ?? title)
            .map((title) => ({
              combineWith: "AND",
              queries: [title],
              prefix: (term) => term === "vol",
            })),
          combineWith: "OR",
        },
      ],
      combineWith: "AND",
    })
    .map((result) => moviesMap.get(result.id as string)!)
    .filter(
      (movie) =>
        movie.year &&
        kinanetMovie.years.some((year) => Math.abs(movie.year! - year) <= 1),
    );

  if (movieResults.length > 1) {
    movieResults = movieResults.filter((movie) =>
      movie.titles.some((title) =>
        kinanetMovie.titles.some(
          (anotherTitle) =>
            idiosyncrasies.titles[anotherTitle] === title ||
            title.toUpperCase() === anotherTitle.toUpperCase(),
        ),
      ),
    );
  }

  return movieResults.length === 1 ? movieResults[0] : null;
}

function findMovieByTitleAndYearAndFuzzyDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  idiosyncrasies: Idiosyncrasies,
  kinanetMovie: KinanetMovie,
): Movie | null {
  let movieResults = movieSearch
    .search({
      queries: [
        {
          fields: ["titles"],
          queries: kinanetMovie.titles
            .map((title) => idiosyncrasies.titles[title.toUpperCase()] ?? title)
            .map((title) => ({
              combineWith: "AND",
              queries: [title],
              prefix: (term) => term === "vol",
            })),
          combineWith: "OR",
        },
        {
          fields: ["directors"],
          queries: kinanetMovie.directors
            .map((director) => idiosyncrasies.directors[director] ?? director)
            .map((director) => ({
              combineWith: "AND",
              queries: [director],
              fuzzy: 0.33,
            })),
          combineWith: "OR",
        },
      ],
      combineWith: "AND",
    })
    .map((result) => moviesMap.get(result.id as string)!)
    .filter(
      (movie) =>
        movie.year &&
        kinanetMovie.years.some((year) => Math.abs(movie.year! - year) <= 1),
    );

  if (movieResults.length > 1) {
    movieResults = movieResults.filter((movie) =>
      movie.titles.some((title) =>
        kinanetMovie.titles.some(
          (anotherTitle) =>
            idiosyncrasies.titles[anotherTitle] === title ||
            title.toUpperCase() === anotherTitle.toUpperCase(),
        ),
      ),
    );
  }

  return movieResults.length === 1 ? movieResults[0] : null;
}

function findMovieByTitleAndYearAndDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  idiosyncrasies: Idiosyncrasies,
  kinanetMovie: KinanetMovie,
): Movie | null {
  let movieResults = movieSearch
    .search({
      queries: [
        {
          fields: ["titles"],
          queries: kinanetMovie.titles
            .map((title) => idiosyncrasies.titles[title.toUpperCase()] ?? title)
            .map((title) => ({
              combineWith: "AND",
              queries: [title],
              prefix: (term) => term === "vol",
            })),
          combineWith: "OR",
        },
        {
          fields: ["directors"],
          queries: kinanetMovie.directors
            .map((director) => idiosyncrasies.directors[director] ?? director)
            .map((director) => ({
              combineWith: "AND",
              queries: [director],
            })),
          combineWith: "OR",
        },
      ],
      combineWith: "AND",
    })
    .map((result) => moviesMap.get(result.id as string)!)
    .filter(
      (movie) =>
        movie.year &&
        kinanetMovie.years.some((year) => Math.abs(movie.year! - year) <= 1),
    );

  if (movieResults.length > 1) {
    movieResults = movieResults.filter((movie) =>
      movie.titles.some((title) =>
        kinanetMovie.titles.some(
          (anotherTitle) =>
            idiosyncrasies.titles[anotherTitle] === title ||
            title.toUpperCase() === anotherTitle.toUpperCase(),
        ),
      ),
    );
  }

  return movieResults.length === 1 ? movieResults[0] : null;
}

function findMovieByYearAndDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  idiosyncrasies: Idiosyncrasies,
  kinanetMovie: KinanetMovie,
): Movie | null {
  assert(
    kinanetMovie.directors.length === 1,
    "Cannot find movie by director if there are multiple directors",
  );

  const directorMovies = findMoviesByDirector(
    movieSearch,
    moviesMap,
    idiosyncrasies,
    kinanetMovie.directors[0],
  );
  let movies = directorMovies;

  movies = movies.filter(
    (movie) =>
      movie.year &&
      kinanetMovie.years.includes(movie.year) &&
      movie.directors.length === 1,
  );

  if (movies.length === 1) {
    // eslint-disable-next-line no-console
    console.log(
      `Isn't it the same movie?\n- ${kinanetMovie.titles.join(" / ")} (${
        kinanetMovie.directors[0]
      }, ${kinanetMovie.years.join("/")})\n- ${movies[0].titles.join(" / ")} (${
        movies[0].directors[0]
      }, ${movies[0].year})`,
    );
  }

  return null;
}

function findMoviesByDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, Movie>,
  idiosyncrasies: Idiosyncrasies,
  director: string,
): Movie[] {
  return movieSearch
    .search({
      combineWith: "AND",
      fields: ["directors"],
      queries: [idiosyncrasies.directors[director] ?? director],
    })
    .map((result) => moviesMap.get(result.id as string)!);
}
