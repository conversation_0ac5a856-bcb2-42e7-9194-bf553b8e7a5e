import * as fs from "node:fs";
import parsePdf from "pdf-parse";

export default class FiveThousandMoviesBookReader {
  constructor(private filepaths: string[]) {}

  async readArticles(): Promise<Article[]> {
    let articles: Article[] = [];

    for (const filepath of this.filepaths) {
      articles = [...articles, ...(await readBookArticles(filepath))];
    }

    return articles.sort((a, b) => b.mark - a.mark);
  }
}

export interface Article {
  directors: string[];
  genre: string;
  titles: string[];
  years: number[];
  mark: number;
}

type ParserState =
  | { type: "table-of-contents" }
  | { type: "heading"; letter: string }
  | {
      type: "article";
      text: string;
      foundDate: false;
    }
  | {
      type: "article";
      text: string;
      foundDate: true;
    };

async function readBookArticles(filepath: string): Promise<Article[]> {
  const bytes = fs.readFileSync(filepath);
  const { text } = await parsePdf(bytes);
  const lines = text.split("\n");
  const articles: (Article | null)[] = [];

  let state: ParserState = { type: "table-of-contents" };

  for (let line of lines) {
    line = line.trim();

    if (state.type === "table-of-contents") {
      if (looksLikeHeading(line)) {
        state = { type: "heading", letter: line };
      }

      continue;
    }

    if (state.type === "heading") {
      if (looksLikeArticleTitle(line)) {
        state = {
          type: "article",
          text: line,
          foundDate: false,
        };
      }

      continue;
    }

    if (!state.foundDate) {
      if (looksLikeDate(line)) {
        state = { ...state, text: state.text + "\n" + line, foundDate: true };
      } else {
        state = { ...state, text: state.text + "\n" + line };
      }

      continue;
    }

    if (state.foundDate) {
      if (looksLikeArticleTitle(line)) {
        articles.push(readArticle(state.text));
        state = {
          type: "article",
          text: line,
          foundDate: false,
        };
      } else if (looksLikeHeading(line)) {
        articles.push(readArticle(state.text));
        state = { type: "heading", letter: line };
      }
    }
  }

  if (state.type === "article") {
    articles.push(readArticle(state.text));
  }

  return articles.filter((article): article is Article => article != null);
}

function readArticle(text: string): Article | null {
  const lines = text.split("\n");
  const markIndex = lines.findIndex((line) => line.startsWith("Оценка -"));
  const directorsIndex = lines.findIndex(
    (line) =>
      line.startsWith("Режиссер ") ||
      line.startsWith("Режиссеры ") ||
      line.startsWith("Режиссёр ") ||
      line.startsWith("Режиссёры "),
  );
  const yearIndex = lines.findIndex((line) => /\. \d{4}/.test(line));

  if (markIndex === -1 || directorsIndex === -1 || yearIndex === -1) {
    return null;
  }

  let title = lines.slice(0, yearIndex).join(" ");
  const originalTitle = title;

  if (title.includes("(")) {
    title = title.slice(0, title.lastIndexOf("(") - 1);
  }

  const titles = title
    .split(" / ")
    .map((title) => (title.startsWith("«") ? title.slice(1) : title))
    .map((title) => (title.endsWith("»") ? title.slice(0, -1) : title));
  const directors = lines[directorsIndex]
    .replace(/(Режиссер|Режиссеры|Режиссёр|Режиссёры) /, "")
    .split(", ")
    .map((director) =>
      director.includes("(")
        ? director.slice(0, director.indexOf("(") - 1)
        : director,
    )
    .flatMap((director) => director.split(" и "))
    .flatMap((director) => director.split(" при участии "))
    .flatMap((director) => director.split(" при неназванном участии "))
    .flatMap((director) => director.split(" под псевдонимом "));
  const years =
    lines[yearIndex]
      .match(/\d{4}/g)
      ?.map((year) => Number(year))
      .filter((year) => !Number.isNaN(year) && year >= 1895) ?? [];
  const genre = lines.find((line, index) => line !== "" && index > markIndex);
  const mark = Number(
    lines[markIndex]
      .slice(0, lines[markIndex].indexOf("("))
      .replace("Оценка -", "")
      .replace(",", "."),
  );

  if (originalTitle.includes("» (")) {
    titles.push(
      ...originalTitle
        .slice(originalTitle.indexOf("» (") + 3, originalTitle.lastIndexOf(")"))
        .split("/")
        .map((title) => title.trim()),
    );
  }

  if (years.length === 0 || genre == null) {
    return null;
  }

  return {
    titles,
    directors,
    genre,
    mark,
    years,
  };
}

function looksLikeHeading(line: string): boolean {
  return /^[А-Я]$/.test(line) || line === "2-3000" || line === "*A-Z";
}

function looksLikeArticleTitle(line: string): boolean {
  line = line.replace(/ +/g, " ");

  return (
    /^[A-Z]+$/.test(line) ||
    (/^«[^»]+?$/.test(line) && /[А-Я]/.test(line)) ||
    /^«[^»]+?»$/.test(line) ||
    /^«[^»]+?» \([^(]+?$/.test(line) ||
    /^«[^»]+?» \([^(]+?$/.test(line) ||
    /^«[^»]+?» \/$/.test(line) ||
    /^«[^»]+?» \/ $/.test(line) ||
    /^«[^»]+?» \/ «[^»]+?» \([^()]+?\)$/.test(line) ||
    /^«[^»]+?» \/ «[^«]+?$/.test(line) ||
    /^«[^»]+?» \/ «[^»]+?» \([^(]+?$/.test(line) ||
    /^[^»]+? \/ «[^«]+?$/.test(line) ||
    /^[^»]+? \/ «[^»]+?» \/ «[^«]+?$/.test(line) ||
    /^«[^»]+?» \/ «[^»]+?» \/ «[^«]+?$/.test(line)
  );
}

function looksLikeDate(line: string): boolean {
  return line.split("/").every((piece) => /^\d{4}$/.test(piece));
}
