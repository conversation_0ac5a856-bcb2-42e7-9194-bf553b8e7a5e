import assert from "node:assert";

export default class MarksSheetReader {
  async readMarks(text: string): Promise<Mark[]> {
    const initialState: ParserState = {
      mark: null,
      marks: [],
      longline: "",
    };

    return text.split("\n").reduce<ParserState>((state, line) => {
      try {
        return reduceLine(state, line);
      } catch (error) {
        // console.error(error.message);

        return state;
      }
    }, initialState).marks;
  }
}

export interface Mark {
  id: string;
  directors: string[];
  mark:
    | 0.5
    | 1
    | 1.5
    | 2
    | 2.5
    | 3
    | 3.5
    | 4
    | 4.5
    | 5
    | 5.5
    | 6
    | 6.5
    | 7
    | 7.5
    | 8
    | 8.5
    | 9
    | 9.5
    | 10;
  titles: string[];
  years: number[];
}

interface ParserState {
  longline: string;
  mark: number | null;
  marks: Mark[];
}

function reduceLine(
  initialState: ParserState,
  initialLine: string,
): ParserState {
  let state = initialState;
  let line = initialLine;

  line = line.trim();

  if (/^Фильмы \d{4}-\d{4} годов$/.test(line) || line.length === 0) {
    return state;
  }

  if (
    /^(10|9,5|9|8,5|8|7,5|7|6,5|6|5|5,5|5|4,5|4|3,5|3|2,5|2|1,5|1|0,5)$/.test(
      line,
    )
  ) {
    return {
      ...state,
      mark: Number(line.replace(",", ".")),
    };
  }

  if (!line.includes(")")) {
    assert(state.mark !== null, "Unspecified mark");

    return {
      ...state,
      longline: state.longline.concat(`${line} `),
    };
  }

  state = {
    ...state,
    longline: state.longline.concat(line),
  };

  const titles = state.longline
    .slice(0, state.longline.lastIndexOf("(") - 1)
    .split(" / ")
    .map((title) =>
      title.startsWith("«") && title.endsWith("»") ? title.slice(1, -1) : title,
    );
  let countries: string[] | null = null;
  let year: number | null = null;
  let directors: string[] | null = null;

  const chunks = state.longline
    .slice(state.longline.lastIndexOf("(") + 1, state.longline.lastIndexOf(")"))
    .split(",")
    .map((chunk) => chunk.trim());

  // eslint-disable-next-line prefer-const
  for (let [index, chunk] of chunks.entries()) {
    if (chunk === "к/м" || chunk === "документальный") {
      // eslint-disable-next-line no-continue
      continue;
    } else if (index === 0) {
      countries = chunk.split("-");
    } else if (/^\d{4}$/.test(chunk)) {
      year = Number(chunk);
    } else if (/^\d{4}-\d{2,4}$/.test(chunk)) {
      year = Number(chunk.slice(0, chunk.indexOf("-")));
    } else if (
      chunk.startsWith("реж.") ||
      chunk.startsWith("режиссёр") ||
      chunk.startsWith("руководитель постановки") ||
      index > 2
    ) {
      directors = (directors ?? ([] as string[])).concat(
        chunk
          .replace("реж.", "")
          .replace("режиссёр", "")
          .replace("оператор", "")
          .replace("руководитель постановки", "")
          .replace("а также не указанных в титрах", "")
          .replace("не указанный в титрах", "")
          .trim()
          .split(" и ")
          .flatMap((x) => x.split(" при участии "))
          .flatMap((x) => x.trim()),
      );
    }
  }

  assert(
    countries !== null,
    `Couldn't parse countries for line "${state.longline}"`,
  );
  assert(
    directors !== null,
    `Couldn't parse directors for line "${state.longline}"`,
  );
  assert(year !== null, `Couldn't parse year for line "${state.longline}"`);
  assert(state.mark !== null, "Unspecified mark");

  state = {
    ...state,
    marks: [
      ...state.marks,
      {
        id: state.longline,
        directors,
        mark: state.mark as Mark["mark"],
        titles,
        years: [year],
      },
    ],
    longline: "",
  };

  return state;
}
