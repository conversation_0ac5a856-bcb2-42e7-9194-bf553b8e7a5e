import assert from "node:assert";

import Mark, { addMarks } from "../domain/Mark.js";
import Watchlist, { removeFromWatchlist } from "../domain/Watchlist.js";
import {
  UserMovieMarkCreatedEvent,
  UserMovieMarkUpdatedEvent,
} from "../jobs/PullKinopoiskMarksJob.js";
import { AccountRepository } from "./Authenticator.js";
import MarkRepository from "./MarkRepository.js";
import WatchlistRepository from "./WatchlistRepository.js";

export async function onMarkCreateOrUpdate(
  accountRepository: AccountRepository,
  markRepository: MarkRepository,
  watchlistRepository: WatchlistRepository,
  events: (UserMovieMarkCreatedEvent | UserMovieMarkUpdatedEvent)[],
): Promise<void> {
  const kinopoiskUserIds = [
    ...new Set(events.map((event) => event.payload.user.id)),
  ];
  const accounts = await accountRepository.findByKinopoiskIds(kinopoiskUserIds);
  const kinopoiskIdsToAccountIdMap = new Map(
    kinopoiskUserIds.map((userId, index) => [userId, accounts[index]?.id]),
  );

  for (const kinopoiskUserId of kinopoiskUserIds) {
    const accountId = kinopoiskIdsToAccountIdMap.get(kinopoiskUserId);

    if (accountId) {
      let marks = await markRepository.find(accountId);
      let watchlist = await watchlistRepository.find(accountId);

      assert(marks, "Expected marks to exist if account exists");
      assert(watchlist, "Expected watchlist to exist if account exists");

      const userEvents = events.filter(
        (event) => event.payload.user.id === kinopoiskUserId,
      );
      const newMarks: Mark[] = convertMarkEventsIntoMarks(userEvents, marks);

      marks = addMarks(marks, newMarks);
      watchlist = removeViewedMoviesFromWatchlist(marks, watchlist);

      await markRepository.set(accountId, marks);
      await watchlistRepository.set(accountId, watchlist);
    }
  }
}

function convertMarkEventsIntoMarks(
  events: (UserMovieMarkCreatedEvent | UserMovieMarkUpdatedEvent)[],
  existingMarks: Mark[],
): Mark[] {
  const viewedMoviesIds = new Set(existingMarks.map((mark) => mark.movieId));
  const marks: Mark[] = events.flatMap((event) => {
    const m: Mark[] = [];
    const alreadyViewed = viewedMoviesIds.has(event.payload.movie.id);

    if (!alreadyViewed) {
      m.push({
        movieId: event.payload.movie.id,
        timestamp: event.payload.timestamp,
        type: "view",
      });
      viewedMoviesIds.add(event.payload.movie.id);
    }

    if (event.payload.score !== null) {
      m.push({
        movieId: event.payload.movie.id,
        mark: event.payload.score,
        timestamp: event.payload.timestamp,
        type: "mark",
      });
    }

    return m;
  });

  return marks;
}

function removeViewedMoviesFromWatchlist(
  marks: Mark[],
  watchlist: Watchlist,
): Watchlist {
  const views = marks.filter((mark) => mark.type === "view");
  const movieIdToViews = new Map<string, Mark[]>();

  for (const view of views) {
    const movieViews = movieIdToViews.get(view.movieId) ?? [];

    movieViews.push(view);
    movieIdToViews.set(view.movieId, movieViews);
  }

  for (const watchlistItem of watchlist) {
    const movieViews = movieIdToViews.get(watchlistItem.movieId);

    if (movieViews?.some((view) => view.timestamp >= watchlistItem.timestamp)) {
      // eslint-disable-next-line no-param-reassign
      watchlist = removeFromWatchlist(watchlist, watchlistItem.movieId);
    }
  }

  return watchlist;
}
