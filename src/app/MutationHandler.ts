import assert from "node:assert";

import { trackMovieView } from "../domain/Mark.js";
import { addToWatchlist, removeFromWatchlist } from "../domain/Watchlist.js";
import Authenticator from "./Authenticator.js";
import MarkRepository from "./MarkRepository.js";
import WatchlistRepository from "./WatchlistRepository.js";

export default class MutationHandler {
  constructor(
    private authenticator: Authenticator,
    private markRepository: MarkRepository,
    private watchlistRepository: WatchlistRepository,
  ) {}

  async trackMovieView(input: TrackMovieViewInput): Promise<void> {
    const now = new Date();
    const account = await this.authenticator.getCurrentAccount();

    assert(account, "Authenticate first please");

    let marks = await this.markRepository.find(account.id);
    let watchlist = await this.watchlistRepository.find(account.id);

    assert(marks, "Expected marks to exist before account is used");
    assert(watchlist, "Expected watchlist to exist before account is used");

    marks = trackMovieView(marks, input.movieId, now);
    watchlist = removeFromWatchlist(watchlist, input.movieId);

    await this.markRepository.set(account.id, marks);
    await this.watchlistRepository.set(account.id, watchlist);
  }

  async addToWatchlist(input: AddToWatchlistInput): Promise<void> {
    const now = new Date();
    const account = await this.authenticator.getCurrentAccount();

    assert(account, "Authenticate first please");

    let watchlist = await this.watchlistRepository.find(account.id);

    assert(watchlist, "Expected watchlist to exist before account is used");

    watchlist = addToWatchlist(watchlist, input.movieId, now);

    await this.watchlistRepository.set(account.id, watchlist);
  }

  async removeFromWatchlist(input: RemoveFromWatchlistInput): Promise<void> {
    const account = await this.authenticator.getCurrentAccount();

    assert(account, "Authenticate first please");

    let watchlist = await this.watchlistRepository.find(account.id);

    assert(watchlist, "Expected watchlist to exist before account is used");

    watchlist = removeFromWatchlist(watchlist, input.movieId);

    await this.watchlistRepository.set(account.id, watchlist);
  }
}

export interface TrackMovieViewInput {
  movieId: string;
}

export interface AddToWatchlistInput {
  movieId: string;
}

export interface RemoveFromWatchlistInput {
  movieId: string;
}
