import assert from "node:assert";

import { AbortError, ConcurrentResource } from "../../lib/concurrent-worker.js";

const MAX_PROXY_FAILURES = 7;

export default class ProxyPoolResource<T> implements ConcurrentResource<T> {
  private readonly freeProxies: T[];
  private readonly takenProxies: T[];
  private readonly failedProxies: T[];
  private readonly proxyFails: Map<T, number>;
  private waiters: ((error?: unknown) => void)[];

  constructor(private readonly proxies: T[]) {
    this.freeProxies = [...proxies];
    this.takenProxies = [];
    this.failedProxies = [];
    this.proxyFails = new Map();
    this.waiters = [];
  }

  isOutOfCapacity(): boolean {
    return this.freeProxies.length === 0 && this.takenProxies.length === 0;
  }

  async take(): Promise<T> {
    if (this.freeProxies.length === 0) {
      return new Promise((resolve, reject) => {
        this.waiters.push((error) => {
          if (error) {
            reject(error);
          } else {
            resolve(this.take());
          }
        });
      });
    }

    const proxy = this.freeProxies.shift();

    assert(proxy, "No free proxies left");

    this.takenProxies.push(proxy);

    assert(
      this.freeProxies.length +
        this.takenProxies.length +
        this.failedProxies.length ===
        this.proxies.length,
      "Proxies invariant violation",
    );

    return proxy;
  }

  release(proxy: T, fail: boolean): void {
    if (fail) {
      this.proxyFails.set(proxy, (this.proxyFails.get(proxy) ?? 0) + 1);
    }

    const shouldPutProxyBack =
      (this.proxyFails.get(proxy) ?? 0) < MAX_PROXY_FAILURES;

    this.takenProxies.splice(this.takenProxies.indexOf(proxy), 1);

    if (shouldPutProxyBack) {
      if (fail) {
        this.freeProxies.push(proxy);
      } else {
        this.freeProxies.unshift(proxy);
      }

      const callback = this.waiters.shift();

      if (callback) {
        callback();
      }
    } else {
      this.failedProxies.push(proxy);
    }
  }

  abort(): void {
    this.waiters.forEach((callback) => callback(new AbortError()));
  }
}
