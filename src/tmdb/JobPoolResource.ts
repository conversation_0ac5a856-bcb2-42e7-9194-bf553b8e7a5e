import assert from "assert";

import { AbortError, ConcurrentResource } from "../../lib/concurrent-worker.js";

export default class JobPoolResource<T> implements ConcurrentResource<T> {
  private readonly freeJobs: T[];
  private readonly takenJobs: T[];
  private readonly doneJobs: T[];
  private readonly waiters: ((error?: unknown) => void)[];

  constructor(private readonly jobs: T[]) {
    this.freeJobs = [...jobs];
    this.takenJobs = [];
    this.doneJobs = [];
    this.waiters = [];
  }

  isOutOfCapacity(): boolean {
    return this.freeJobs.length === 0 && this.takenJobs.length === 0;
  }

  getFailedJobsCount(): number {
    return this.freeJobs.length + this.takenJobs.length;
  }

  async take(): Promise<T> {
    if (this.freeJobs.length === 0) {
      return new Promise((resolve, reject) => {
        this.waiters.push((error) => {
          if (error) {
            reject(error);
          } else {
            resolve(this.take());
          }
        });
      });
    }

    const job = this.freeJobs.shift();

    assert(job, "No free jobs left");

    this.takenJobs.push(job);

    assert(
      this.freeJobs.length + this.takenJobs.length + this.doneJobs.length ===
        this.jobs.length,
      "Jobs invariant violation",
    );

    return job;
  }

  release(input: T, fail: boolean): void {
    this.takenJobs.splice(this.takenJobs.indexOf(input), 1);

    if (fail) {
      this.freeJobs.push(input);

      const callback = this.waiters.shift();

      if (callback) {
        callback();
      }
    } else {
      this.doneJobs.push(input);
    }
  }

  abort(): void {
    this.waiters.forEach((callback) => callback(new AbortError()));
  }
}
