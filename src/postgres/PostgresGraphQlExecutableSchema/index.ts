/* eslint-disable no-underscore-dangle */
import assert from "node:assert";
import { makeExecutableSchema } from "@graphql-tools/schema";

import typeDefs from "../../web/graphql/schema.graphql.js";
import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";
import Genre from "./Genre.js";
import Movie from "./Movie.js";
import Mutation from "./Mutation.js";
import Person from "./Person.js";
import Query from "./Query.js";
import User from "./User.js";

const resolvers: graphql.Resolvers<PostgresGraphQlResolverContext> = {
  Query,
  Mutation,
  Genre,
  Movie,
  Person,
  User,
};

const executableSchema = makeExecutableSchema({
  typeDefs,
  resolvers,
});

for (const [name, resolver] of Object.entries(resolvers)) {
  const type = executableSchema.getType(name);

  assert(type, `Type with name ${name} not found in schema`);

  const extensions = "__extensions" in resolver && resolver.__extensions;

  if (extensions) {
    Object.assign(type.extensions, extensions);
  }
}

export default executableSchema;
