/* eslint-disable no-underscore-dangle */
import assert from "node:assert";
import { GraphQLUnionTypeExtensions } from "graphql";

import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";
import sql from "../sql.js";
import { batchResolveNested } from "./_common.js";

const Genre: graphql.GenreResolvers<PostgresGraphQlResolverContext> & {
  __extensions?: GraphQLUnionTypeExtensions;
} = {
  __extensions: {
    preloadBatch: async (
      parents: graphql.User[],
      context: PostgresGraphQlResolverContext,
    ): Promise<graphql.User[]> => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversParentTypes["User"]
        >(sql`
            SELECT
              graphql_genre.id::text as "id",
              graphql_genre.label as "label",
              graphql_genre.slug as "slug"
            FROM
              graphql_genre
            WHERE
              graphql_genre.id IN (${sql.raw(
                parents.map((parent) => Number(parent.id)).join(", "),
              )})
          `);

        const rowById = new Map(rows.map((row) => [row.id, row]));

        return parents.map((parent) => {
          const row = rowById.get(parent.id);

          assert(row, `Cannot find Genre with id "${parent.id}"`);

          return row;
        });
      });
    },
  },

  movies: {
    resolve: batchResolveNested(async (parent, args, context) => {
      return context.pool.transaction(async (connection) => {
        await connection.query(
          sql`SELECT SETSEED(EXTRACT(DOY FROM NOW()) / 367.0);`,
        );

        const movies = await connection.query<
          graphql.ResolversTypes["Movie"]
        >(sql`
            SELECT
              graphql_movie.id::text as "id"
            FROM
              graphql_movie
            JOIN
              graphql_movie_genre
              ON graphql_movie_genre.movie_id = graphql_movie.id
            LEFT JOIN
              graphql_movie_top
              ON graphql_movie_top.movie_id = graphql_movie.id
              AND graphql_movie_top.user_id = ${Number(context.accountId)}
            WHERE
              graphql_movie_genre.genre_id = ${Number(parent.id)}
              AND (
                ${args.sort !== graphql.MovieSort.Random}
                OR graphql_movie_top.good_marks_percentage >= 70
                OR graphql_movie_top.best_marks_percentage >= 5
              )
              AND (${args.viewed !== true} OR EXISTS (
                SELECT
                FROM
                  graphql_user_movie_view
                WHERE
                  graphql_user_movie_view.movie_id = graphql_movie.id
                  AND graphql_user_movie_view.user_id = ${Number(
                    context.accountId,
                  )}
                )
              )
              AND (${args.viewed !== false} OR NOT EXISTS (
                SELECT
                FROM
                  graphql_user_movie_view
                WHERE
                  graphql_user_movie_view.movie_id = graphql_movie.id
                  AND graphql_user_movie_view.user_id = ${Number(
                    context.accountId,
                  )}
                )
              )
            ORDER BY
              ${sql.raw(
                {
                  [graphql.MovieSort
                    .Chronological]: `graphql_movie.year ASC, graphql_movie.id DESC`,
                  [graphql.MovieSort
                    .ReverseChronological]: `graphql_movie.year DESC, graphql_movie.id DESC`,
                  [graphql.MovieSort
                    .BestFirst]: `graphql_movie_top."position" ASC, graphql_movie.id DESC`,
                  [graphql.MovieSort.Random]: `RANDOM()`,
                }[args.sort],
              )}
            OFFSET
              ${args.offset ?? 0}
            LIMIT
              ${Math.min(args.limit ?? 10, 250)}
        `);

        return movies;
      });
    }),
  },
};

export default Genre;
