/* eslint-disable no-underscore-dangle */
import {
  GraphQLList,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLOutputType,
  GraphQLUnionType,
} from "graphql";
import graphQlFields from "graphql-fields";

import * as graphql from "../../web/graphql/types.generated.js";

export type FieldSet = Record<string, unknown>;

/* eslint-disable */
declare module "graphql" {
  interface GraphQLObjectTypeExtensions<_TSource = any, _TContext = any> {
    preloadBatch?: (
      parents: _TSource[],
      context: _TContext,
      fieldSet: FieldSet,
    ) => Promise<_TSource[]>;
  }
}
/* eslint-enable */

export function batchResolveNested<R, P, C, A>(
  originalResolver: graphql.ResolverFn<R, P, C, A>,
): graphql.ResolverFn<R, P, C, A> {
  return async (parent, args, context, resolveInfo) => {
    let result = await originalResolver(parent, args, context, resolveInfo);
    const fieldSet = graphQlFields(resolveInfo);
    let jobs: {
      fieldSet: FieldSet;
      value: unknown;
      type: GraphQLOutputType;
    }[] = [
      {
        fieldSet,
        value: result,
        type: resolveInfo.returnType,
      },
    ];
    const repository: Map<
      FieldSet,
      {
        resolver: (
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          parents: any[],
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          context: any,
          fieldSet: FieldSet,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ) => Promise<any[]>;
        originalToResolved: Map<unknown, unknown>;
        resolvedToOriginal: Map<unknown, unknown>;
      }
    > = new Map();

    do {
      const thisCycleJobs = jobs;
      jobs = [];

      const toBeResolved: Map<FieldSet, unknown[]> = new Map();

      for (const job of thisCycleJobs) {
        // eslint-disable-next-line no-loop-func
        forEach(job.value, job.type, job.fieldSet, (value, type, fieldSet) => {
          if (type instanceof GraphQLObjectType) {
            if (!repository.has(fieldSet) && type.extensions.preloadBatch) {
              repository.set(fieldSet, {
                resolver: type.extensions.preloadBatch,
                originalToResolved: new Map<unknown, unknown>(),
                resolvedToOriginal: new Map<unknown, unknown>(),
              });
            }

            if (
              repository.has(fieldSet) &&
              !repository.get(fieldSet)!.resolvedToOriginal.has(value)
            ) {
              if (!toBeResolved.has(fieldSet)) {
                toBeResolved.set(fieldSet, []);
              }

              toBeResolved.get(fieldSet)!.push(value);
              jobs.push({ fieldSet, value, type });
            }
          }
        });
      }

      for (const [fieldSet, toResolve] of toBeResolved.entries()) {
        if (toResolve.length > 0) {
          const subrepository = repository.get(fieldSet)!;
          const resolved = await subrepository.resolver(
            toResolve,
            context,
            fieldSet,
          );

          for (const [index, original] of toResolve.entries()) {
            subrepository.originalToResolved.set(original, resolved[index]);
            subrepository.resolvedToOriginal.set(resolved[index], original);
          }
        }
      }

      jobs = jobs.map((job) => {
        if (
          job.type instanceof GraphQLObjectType &&
          repository.has(job.fieldSet) &&
          repository.get(job.fieldSet)!.originalToResolved.has(job.value)
        ) {
          return {
            fieldSet: job.fieldSet,
            value: repository
              .get(job.fieldSet)!
              .originalToResolved.get(job.value),
            type: job.type,
          };
        }

        return job;
      });
    } while (jobs.length > 0);

    result = map(
      result,
      resolveInfo.returnType,
      fieldSet,
      (value, type, fieldSet) => {
        if (
          type instanceof GraphQLObjectType &&
          repository.has(fieldSet) &&
          repository.get(fieldSet)!.originalToResolved.has(value)
        ) {
          return repository.get(fieldSet)!.originalToResolved.get(value);
        }

        return value;
      },
    );

    return result;
  };
}

function forEach(
  value: unknown,
  type: GraphQLOutputType,
  fieldSet: FieldSet,
  callback: (
    value: unknown,
    type: GraphQLOutputType,
    fieldsSet: FieldSet,
  ) => void,
): void {
  if (type instanceof GraphQLList) {
    for (const v of value as unknown[]) {
      forEach(v, type.ofType, fieldSet, callback);
    }
  } else if (type instanceof GraphQLNonNull) {
    forEach(value, type.ofType, fieldSet, callback);
  } else if (type instanceof GraphQLObjectType && value != null) {
    callback(value, type, fieldSet);

    if (value != null) {
      const fields = type.getFields();

      for (const [key, v] of Object.entries(value as object)) {
        if (fields[key]) {
          forEach(v, fields[key].type, fieldSet?.[key] as FieldSet, callback);
        }
      }
    }
  } else if (type instanceof GraphQLUnionType) {
    const typename =
      (value as { __typename: string }).__typename ??
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      type.resolveType?.(value, null, null as any, null as any);
    const subtype = type
      .getTypes()
      .find((s) => s.name === typename) as GraphQLOutputType;

    forEach(value, subtype, fieldSet, callback);
  }
}

function map<T>(
  value: T,
  type: GraphQLOutputType,
  fieldSet: FieldSet,
  callback: (
    value: unknown,
    type: GraphQLOutputType,
    fieldSet: FieldSet,
  ) => unknown,
): T {
  if (type instanceof GraphQLList) {
    return (value as T[]).map((v) =>
      map(v, type.ofType, fieldSet, callback),
    ) as T;
  }
  if (type instanceof GraphQLNonNull) {
    return map(value, type.ofType, fieldSet, callback);
  }
  if (type instanceof GraphQLObjectType) {
    value = callback(value, type, fieldSet) as T;

    if (value != null) {
      const fields = type.getFields();

      for (const key of Object.keys(value as object)) {
        if (fields[key]) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (value as any)[key] = map(
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (value as any)[key],
            fields[key].type,
            fieldSet?.[key] as FieldSet,
            callback,
          );
        }
      }
    }

    return value;
  }
  if (type instanceof GraphQLUnionType) {
    const typename =
      (value as { __typename: string }).__typename ??
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      type.resolveType?.(value, null, null as any, null as any);
    const subtype = type
      .getTypes()
      .find((s) => s.name === typename) as GraphQLOutputType;

    return map(value, subtype, fieldSet, callback);
  }

  return value;
}
