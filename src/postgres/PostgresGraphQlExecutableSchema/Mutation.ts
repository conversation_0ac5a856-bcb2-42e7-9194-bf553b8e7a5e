/* eslint-disable no-underscore-dangle */
import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";

const Mutation: graphql.MutationResolvers<PostgresGraphQlResolverContext> = {
  trackMovieView: {
    resolve: async (_prev, args, context) => {
      await context.mutationHandler.trackMovieView({
        movieId: args.movieId,
      });

      return null;
    },
  },

  addToWatchlist: {
    resolve: async (_prev, args, context) => {
      await context.mutationHandler.addToWatchlist({
        movieId: args.movieId,
      });

      return null;
    },
  },

  removeFromWatchlist: {
    resolve: async (_prev, args, context) => {
      await context.mutationHandler.removeFromWatchlist({
        movieId: args.movieId,
      });

      return null;
    },
  },
};

export default Mutation;
