import pg from "pg";

import Connection from "./Connection.js";

// pg by default interprets dates with TIMESTAMP WITHOUT TIMEZONE type as local time
// @see https://github.com/brianc/node-postgres/issues/2141
pg.types.setTypeParser(pg.types.builtins.TIMESTAMP, (val) => {
  return val ? new Date(`${val}Z`) : null;
});

export default class ConnectionPool {
  private pgPool: pg.Pool;

  constructor(connectionString: string) {
    this.pgPool = new pg.Pool({ connectionString });
  }

  async transaction<T>(
    callback: (connection: Connection) => Promise<T>,
  ): Promise<T> {
    const pgClient = await this.pgPool.connect();

    try {
      const connection = new Connection(pgClient);

      return await callback(connection);
    } finally {
      pgClient.release();
    }
  }
}
