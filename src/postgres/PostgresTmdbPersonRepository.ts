import { Person, PersonRepository } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbPersonRepository implements PersonRepository {
  constructor(private pool: ConnectionPool) {}

  async findMany(tmdbIds: string[]): Promise<(Person | null)[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        images_idxs: number[] | null;
        images_urls: string[] | null;
        images_widths: number[] | null;
        images_heights: number[] | null;
      }>(sql`
        SELECT
          id,
          images.idxs as images_idxs,
          images.urls as images_urls,
          images.widths as images_widths,
          images.heights as images_heights
        FROM
          tmdb_person
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_person_image.order ORDER BY "order" ASC, width ASC, url DESC) as idxs,
              ARRAY_AGG(tmdb_person_image.url ORDER BY "order" ASC, width ASC, url DESC) as urls,
              ARRAY_AGG(tmdb_person_image.width ORDER BY "order" ASC, width ASC, url DESC) as widths,
              ARRAY_AGG(tmdb_person_image.height ORDER BY "order" ASC, width ASC, url DESC) as heights
            FROM
              tmdb_person_image
            WHERE
              tmdb_person_image.person_id = tmdb_person.id
          ) images ON true
        WHERE
          id IN (${sql.raw(tmdbIds.map((id) => `${id}`).join(", "))})
      `);
      const people: Person[] = rows.map((row) => ({
        id: String(row.id),
        images:
          row.images_urls &&
          row.images_idxs &&
          row.images_widths &&
          row.images_heights
            ? groupBy(
                zip4(
                  row.images_urls,
                  row.images_idxs,
                  row.images_widths,
                  row.images_heights,
                ).map(([url, index, width, height]) => ({
                  height,
                  index,
                  url,
                  width,
                })),
                (item) => String(item.index),
              ).map((group) => ({
                sizes: group.items.map((item) => ({
                  height: item.height,
                  width: item.width,
                  url: item.url,
                })),
              }))
            : [],
      }));
      const hash = new Map(people.map((m) => [m.id, m]));

      return tmdbIds.map((id) => hash.get(id) ?? null);
    });
  }

  async findAll(): Promise<Person[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        images_idxs: number[] | null;
        images_urls: string[] | null;
        images_widths: number[] | null;
        images_heights: number[] | null;
      }>(sql`
        SELECT
          id,
          images.idxs as images_idxs,
          images.urls as images_urls,
          images.widths as images_widths,
          images.heights as images_heights
        FROM
          tmdb_person
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_person_image.order ORDER BY "order" ASC, width ASC, url DESC) as idxs,
              ARRAY_AGG(tmdb_person_image.url ORDER BY "order" ASC, width ASC, url DESC) as urls,
              ARRAY_AGG(tmdb_person_image.width ORDER BY "order" ASC, width ASC, url DESC) as widths,
              ARRAY_AGG(tmdb_person_image.height ORDER BY "order" ASC, width ASC, url DESC) as heights
            FROM
              tmdb_person_image
            WHERE
              tmdb_person_image.person_id = tmdb_person.id
          ) images ON true
        ORDER BY
          tmdb_person.updated_at DESC
      `);

      return rows.map((row) => ({
        id: String(row.id),
        images:
          row.images_urls &&
          row.images_idxs &&
          row.images_widths &&
          row.images_heights
            ? groupBy(
                zip4(
                  row.images_urls,
                  row.images_idxs,
                  row.images_widths,
                  row.images_heights,
                ).map(([url, index, width, height]) => ({
                  height,
                  index,
                  url,
                  width,
                })),
                (item) => String(item.index),
              ).map((group) => ({
                sizes: group.items.map((item) => ({
                  height: item.height,
                  width: item.width,
                  url: item.url,
                })),
              }))
            : [],
      }));
    });
  }

  async setMany(people: Person[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_person (LIKE tmdb_person)`,
      );
      await connection.copyFrom(
        "COPY tmp_tmdb_person (id, created_at, updated_at) FROM STDIN",
        people.map((person) => ({
          id: Number(person.id),
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
            INSERT INTO tmdb_person
            (SELECT * FROM tmp_tmdb_person)
            ON CONFLICT (id) DO NOTHING
          `,
      );
      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_person_image (LIKE tmdb_person_image)`,
      );
      await connection.copyFrom(
        `COPY tmp_tmdb_person_image (person_id, "order", "url", height, width, created_at, updated_at) FROM STDIN`,
        people
          .map((person) =>
            person.images
              .map((image, index) =>
                image.sizes.map((size) => ({
                  person_id: Number(person.id),
                  order: index,
                  url: size.url,
                  height: size.height,
                  width: size.width,
                  created_at: new Date(),
                  updated_at: new Date(),
                })),
              )
              .reduce((acc, x) => [...acc, ...x], []),
          )
          .reduce((acc, x) => [...acc, ...x], []),
      );
      await connection.query(
        sql`
            INSERT INTO tmdb_person_image
            (SELECT * FROM tmp_tmdb_person_image)
            ON CONFLICT (person_id, "order", "url")
              DO UPDATE SET height = excluded.height,
                            width = excluded.width,
                            updated_at = excluded.updated_at
              WHERE (tmdb_person_image.height, tmdb_person_image.width) IS DISTINCT FROM (excluded.height, excluded.width)
          `,
      );
      await connection.query(
        sql`
            DELETE FROM
              tmdb_person_image
            WHERE
              EXISTS (
                SELECT
                FROM
                  tmp_tmdb_person
                WHERE
                  tmp_tmdb_person.id = tmdb_person_image.person_id
              )
              AND NOT EXISTS (
                SELECT
                FROM
                  tmp_tmdb_person_image
                WHERE
                  tmp_tmdb_person_image.person_id = tmdb_person_image.person_id
                  AND tmp_tmdb_person_image."order" = tmdb_person_image."order"
                  AND tmp_tmdb_person_image.url = tmdb_person_image.url
              )
          `,
      );
      await connection.query(sql`DROP TABLE tmp_tmdb_person`);
      await connection.query(sql`DROP TABLE tmp_tmdb_person_image`);
    });
  }
}

function zip4<A, B, C, D>(as: A[], bs: B[], cs: C[], ds: D[]): [A, B, C, D][] {
  const result: [A, B, C, D][] = [];
  const length = Math.min(as.length, bs.length, cs.length, ds.length);

  for (let i = 0; i < length; i += 1) {
    result.push([as[i], bs[i], cs[i], ds[i]]);
  }

  return result;
}

function groupBy<T, K>(
  items: T[],
  fn: (item: T) => K,
): { key: K; items: T[] }[] {
  const groups: { key: K; items: T[] }[] = [];

  items.forEach((item) => {
    const key = fn(item);
    const group = groups.find((g) => g.key === key);

    if (group) {
      group.items.push(item);
    } else {
      groups.push({
        key,
        items: [item],
      });
    }
  });

  return groups;
}
