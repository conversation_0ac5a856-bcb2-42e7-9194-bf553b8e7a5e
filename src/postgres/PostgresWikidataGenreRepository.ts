import { Genre, GenreRepository } from "../jobs/PullWikidataMoviesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresWikidataGenreRepository
  implements GenreRepository
{
  constructor(private pool: ConnectionPool) {}

  async findAll(): Promise<Genre[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        label_en: string | null;
        label_ru: string | null;
        wikipedia_en_slug: string | null;
        wikipedia_ru_slug: string | null;
      }>(sql`
        SELECT
          id,
          label_en,
          label_ru,
          wikipedia_en_slug,
          wikipedia_ru_slug
        FROM
          wikidata_genre
      `);
      const genres: Genre[] = rows.map((row) => ({
        id: String(row.id),
        label: {
          en: row.label_en ?? undefined,
          ru: row.label_ru ?? undefined,
        },
        wikipediaSlugs: {
          en: row.wikipedia_en_slug ?? undefined,
          ru: row.wikipedia_ru_slug ?? undefined,
        },
      }));

      return genres;
    });
  }

  async findMany(ids: string[]): Promise<(Genre | null)[]> {
    if (ids.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        label_en: string | null;
        label_ru: string | null;
        wikipedia_en_slug: string | null;
        wikipedia_ru_slug: string | null;
      }>(sql`
        SELECT
          id,
          label_en,
          label_ru,
          wikipedia_en_slug,
          wikipedia_ru_slug
        FROM
          wikidata_genre
        WHERE
          id IN (${sql.raw(ids.map((id) => `${id}`).join(", "))})
      `);
      const genres: Genre[] = rows.map((row) => ({
        id: String(row.id),
        label: {
          en: row.label_en ?? undefined,
          ru: row.label_ru ?? undefined,
        },
        wikipediaSlugs: {
          en: row.wikipedia_en_slug ?? undefined,
          ru: row.wikipedia_ru_slug ?? undefined,
        },
      }));
      const hash = new Map(genres.map((p) => [p.id, p]));

      return ids.map((id) => hash.get(id) ?? null);
    });
  }

  async setMany(people: Genre[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_wikidata_genre (LIKE wikidata_genre)`,
      );
      await connection.copyFrom(
        "COPY tmp_wikidata_genre (id, label_en, label_ru, wikipedia_en_slug, wikipedia_ru_slug, created_at, updated_at) FROM STDIN",
        people.map((genre) => ({
          id: Number(genre.id),
          label_en: genre.label.en,
          label_ru: genre.label.ru,
          wikipedia_en_slug: genre.wikipediaSlugs.en,
          wikipedia_ru_slug: genre.wikipediaSlugs.ru,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO wikidata_genre
          (SELECT * FROM tmp_wikidata_genre)
          ON CONFLICT (id)
            DO UPDATE SET label_en = excluded.label_en,
                          label_ru = excluded.label_ru,
                          wikipedia_en_slug = excluded.wikipedia_en_slug,
                          wikipedia_ru_slug = excluded.wikipedia_ru_slug,
                          updated_at = excluded.updated_at
            WHERE (
              wikidata_genre.label_en,
              wikidata_genre.label_ru,
              wikidata_genre.wikipedia_en_slug,
              wikidata_genre.wikipedia_ru_slug
            ) IS DISTINCT FROM (
              excluded.label_en,
              excluded.label_ru,
              excluded.wikipedia_en_slug,
              excluded.wikipedia_ru_slug
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_wikidata_genre`);
    });
  }
}
