import Connection from "./Connection.js";
import sql from "./sql.js";

export interface FillMoviesSubtitlesInput {
  kinopoiskId: string;
  subtitle: string;
}

export async function fillMoviesSubtitles(
  connection: Connection,
  inputs: FillMoviesSubtitlesInput[],
): Promise<void> {
  await connection.query(sql`
    CREATE TEMPORARY TABLE tmp_zyr_movie (
      id INT NOT NULL,
      subtitle TEXT
    );
  `);
  await connection.copyFrom(
    "COPY tmp_zyr_movie (id, subtitle) FROM STDIN",
    inputs.map((row) => ({
      id: Number(row.kinopoiskId),
      slug: row.subtitle,
    })),
  );
  await connection.mutate(sql`
    UPDATE
      zyr_movie
    SET
      subtitle = NULL
  `);
  await connection.mutate(sql`
    UPDATE
      zyr_movie
    SET
      subtitle = tmp_zyr_movie.subtitle
    FROM
      tmp_zyr_movie
    WHERE
      tmp_zyr_movie.id = zyr_movie.id
      AND (
        tmp_zyr_movie.subtitle != zyr_movie.subtitle
        OR (tmp_zyr_movie.subtitle IS NULL) != (zyr_movie.subtitle IS NULL)
      )
  `);
}
