import MarkRepository from "../app/MarkRepository.js";
import Mark from "../domain/Mark.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresMarkRepository implements MarkRepository {
  constructor(private pool: ConnectionPool) {}

  async find(accountId: string): Promise<Mark[] | null> {
    return this.pool.transaction(async (connection) => {
      const rawMarks = await connection.query<{
        movie_id: number;
        mark: number;
        timestamp: Date;
      }>(sql`
        SELECT
          movie_id,
          mark,
          timestamp
        FROM
          zyr_movie_mark_history
        WHERE
          account_id = ${Number(accountId)}
      `);

      const marks: Mark[] = rawMarks.map((rawMark) =>
        rawMark.mark === null
          ? {
              movieId: String(rawMark.movie_id),
              timestamp: rawMark.timestamp,
              type: "view",
            }
          : {
              movieId: String(rawMark.movie_id),
              mark: rawMark.mark,
              timestamp: rawMark.timestamp,
              type: "mark",
            },
      );

      return marks;
    });
  }

  async set(accountId: string, marks: Mark[]): Promise<void> {
    await this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_zyr_movie_mark_history (LIKE zyr_movie_mark_history)`,
      );
      await connection.copyFrom(
        "COPY tmp_zyr_movie_mark_history (account_id, movie_id, mark, timestamp) FROM STDIN",
        marks.map((change) => ({
          account_id: Number(accountId),
          movie_id: Number(change.movieId),
          mark: change.type === "mark" ? change.mark : null,
          timestamp: change.timestamp,
        })),
      );

      await connection.mutate(
        sql`
          INSERT INTO zyr_movie_mark_history
          (
            SELECT
              *
            FROM
              tmp_zyr_movie_mark_history
            WHERE
              NOT EXISTS (
                SELECT
                  *
                FROM
                  zyr_movie_mark_history  
                WHERE
                  zyr_movie_mark_history.account_id = tmp_zyr_movie_mark_history.account_id
                  AND zyr_movie_mark_history.movie_id = tmp_zyr_movie_mark_history.movie_id
                  AND zyr_movie_mark_history.mark IS NOT DISTINCT FROM tmp_zyr_movie_mark_history.mark
                  AND zyr_movie_mark_history.timestamp = tmp_zyr_movie_mark_history.timestamp
              )
          )
        `,
      );

      await connection.mutate(
        sql`
          DELETE FROM zyr_movie_mark_history
          WHERE
            zyr_movie_mark_history.account_id = ${Number(accountId)}
            AND NOT EXISTS (
              SELECT
                *
              FROM
                tmp_zyr_movie_mark_history
              WHERE
                tmp_zyr_movie_mark_history.account_id = zyr_movie_mark_history.account_id
                AND tmp_zyr_movie_mark_history.movie_id = zyr_movie_mark_history.movie_id
                AND tmp_zyr_movie_mark_history.mark IS NOT DISTINCT FROM zyr_movie_mark_history.mark
                AND tmp_zyr_movie_mark_history.timestamp = zyr_movie_mark_history.timestamp
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_zyr_movie_mark_history`);
    });
  }
}
