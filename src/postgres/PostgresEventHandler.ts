import { onMarkCreateOrUpdate } from "../app/KinopoiskEventHandler.js";
import { BatchEventBus } from "../domain/EventBus.js";
import { Event as ComposeLoglinesJobEvent } from "../jobs/ComposeLoglinesJob.js";
import {
  Event as PullKinopoiskMarksJobEvent,
  UserMovieMarkCreatedEvent,
  UserMovieMarkUpdatedEvent,
} from "../jobs/PullKinopoiskMarksJob.js";
import { Event as PullTmdbContentJob } from "../jobs/PullTmdbContentJob.js";
import { Event as PullWikidataMoviesJobEvent } from "../jobs/PullWikidataMoviesJob.js";
import Connection from "./Connection.js";
import ConnectionPool from "./ConnectionPool.js";
import PostgresAccountRepository from "./PostgresAccountRepository.js";
import PostgresMarkRepository from "./PostgresMarkRepository.js";
import PostgresWatchlistRepository from "./PostgresWatchlistRepository.js";
import sql from "./sql.js";

type Event =
  | ComposeLoglinesJobEvent
  | PullKinopoiskMarksJobEvent
  | PullTmdbContentJob
  | PullWikidataMoviesJobEvent;

export default class PostgresEventHandler implements BatchEventBus<Event> {
  private seenTypes = new Set<Event["type"]>();
  private markEvents: (
    | UserMovieMarkCreatedEvent
    | UserMovieMarkUpdatedEvent
  )[] = [];

  constructor(
    private debug: (message: string) => void,
    private pool: ConnectionPool,
  ) {}

  push(event: Event): void {
    if (
      event.type === "UserMovieMarkCreatedEvent" ||
      event.type === "UserMovieMarkUpdatedEvent"
    ) {
      this.markEvents.push(event);
    }

    this.seenTypes.add(event.type);
  }

  async batch(): Promise<void> {
    await this.pool.transaction(async (connection) => {
      if (this.seenTypes.has("MovieCreatedEvent")) {
        this.debug("Initializing new movies...");
        await initializeNewMovies(connection);
      }

      if (this.seenTypes.has("MovieTitleUpdatedEvent")) {
        this.debug("Composing movie slugs...");
        await fillMoviesSlugs(connection);
      }

      if (
        this.seenTypes.has("MovieCreatedEvent") ||
        this.seenTypes.has("LoglineComposedEvent") ||
        this.seenTypes.has("MovieDurationUpdatedEvent") ||
        this.seenTypes.has("MovieTitleUpdatedEvent") ||
        this.seenTypes.has("MovieWikipediaSlugUpdatedEvent") ||
        this.seenTypes.has("MovieYearUpdatedEvent")
      ) {
        this.debug("Refreshing movie cache...");
        await refreshMoviesCache(connection);
      }

      if (this.seenTypes.has("MovieGenresUpdatedEvent")) {
        this.debug("Refreshing movie genres cache...");
        await refreshMovieGenresCache(connection);
      }

      if (
        this.seenTypes.has("ImageDeletedEvent") ||
        this.seenTypes.has("ImageDownloadedEvent") ||
        this.seenTypes.has("MovieImagesUpdatedEvent") ||
        this.seenTypes.has("MovieTmdbIdUpdatedEvent")
      ) {
        this.debug("Refreshing movie images cache...");
        await refreshMovieImagesCache(connection);
      }

      if (this.seenTypes.has("PersonCreatedEvent")) {
        this.debug("Initializing new people...");
        await initializeNewPeople(connection);
      }

      if (this.seenTypes.has("PersonFullNameUpdatedEvent")) {
        this.debug("Composing person slugs...");
        await fillPeopleSlugs(connection);
      }

      if (
        this.seenTypes.has("PersonFullNameUpdatedEvent") ||
        this.seenTypes.has("PersonWikipediaSlugUpdatedEvent")
      ) {
        this.debug("Refreshing person cache...");
        await refreshPeopleCache(connection);
      }

      if (
        this.seenTypes.has("ImageDeletedEvent") ||
        this.seenTypes.has("ImageDownloadedEvent") ||
        this.seenTypes.has("PersonImagesUpdatedEvent") ||
        this.seenTypes.has("PersonTmdbIdUpdatedEvent")
      ) {
        this.debug("Refreshing person images cache...");
        await refreshPersonImageCache(connection);
      }

      if (
        this.seenTypes.has("UserMovieMarkCreatedEvent") ||
        this.seenTypes.has("UserMovieMarkDeletedEvent") ||
        this.seenTypes.has("UserMovieMarkUpdatedEvent")
      ) {
        this.debug("Refreshing friends...");
        await refreshFriendsCache(connection);
        this.debug("Refreshing movie top...");
        await refreshMovieRatingCache(connection);
      }
    });

    if (this.markEvents.length > 0) {
      const accountRepository = new PostgresAccountRepository(this.pool);
      const markRepository = new PostgresMarkRepository(this.pool);
      const watchlistRepository = new PostgresWatchlistRepository(this.pool);

      await onMarkCreateOrUpdate(
        accountRepository,
        markRepository,
        watchlistRepository,
        this.markEvents,
      );
    }
  }
}

async function initializeNewMovies(connection: Connection): Promise<void> {
  await connection.query(sql`CREATE TEMP TABLE tmp_zyr_movie (LIKE zyr_movie)`);
  /* eslint-disable no-irregular-whitespace */
  await connection.query(sql`
    INSERT INTO tmp_zyr_movie (id) (
      SELECT DISTINCT ON (kinopoisk_movie.id)
        kinopoisk_movie.id as id
      FROM
        kinopoisk_movie
      ORDER BY
        kinopoisk_movie.id ASC
    )
  `);
  /* eslint-enable no-irregular-whitespace */
  await connection.query(
    sql`
      INSERT INTO zyr_movie
      (
        SELECT
          *
        FROM
          tmp_zyr_movie
      )
      ON CONFLICT (id)
        DO NOTHING
    `,
  );
  await connection.query(sql`DROP TABLE tmp_zyr_movie`);
}

async function refreshMoviesCache(connection: Connection): Promise<void> {
  await connection.query(sql`REFRESH MATERIALIZED VIEW "graphql_movie"`);
}

async function refreshMovieImagesCache(connection: Connection): Promise<void> {
  await connection.query(sql`REFRESH MATERIALIZED VIEW "graphql_movie_image"`);
}

async function refreshMovieGenresCache(connection: Connection): Promise<void> {
  await connection.query(sql`REFRESH MATERIALIZED VIEW "graphql_movie_genre"`);
}

async function fillMoviesSlugs(connection: Connection): Promise<void> {
  const rows = await connection.query<{
    id: number;
    slug: string | null;
    year: number | null;
    title_en: string | null;
  }>(sql`
    SELECT
      zyr_movie.id as "id",
      zyr_movie.slug as "slug",
      kinopoisk_movie.year as "year",
      wikidata_movie.title_en as "title_en"
    FROM
      zyr_movie
    LEFT JOIN
      kinopoisk_movie
      ON kinopoisk_movie.id = zyr_movie.id
    LEFT JOIN
      wikidata_movie
      ON wikidata_movie.kinopoisk_id = zyr_movie.id
  `);

  const slugToId = new Map<string, number>();
  const idToSlug = new Map<number, string>();

  rows.forEach((row) => {
    if (row.slug) {
      slugToId.set(row.slug, row.id);
      idToSlug.set(row.id, row.slug);
    }
  });

  rows.forEach((row) => {
    if (idToSlug.has(row.id)) {
      return;
    }

    const slugs: string[] = [];

    if (row.title_en) {
      slugs.push(slugify(row.title_en));
    }

    if (row.title_en && row.year) {
      slugs.push(slugify(`${row.title_en} ${row.year}`));

      for (let i = 1; i <= 3; i += 1) {
        slugs.push(slugify(`${row.title_en} ${row.year} ${i}`));
      }
    }

    for (const slug of slugs) {
      if (!slugToId.has(slug)) {
        slugToId.set(slug, row.id);
        idToSlug.set(row.id, slug);
        // eslint-disable-next-line no-param-reassign
        row.slug = slug;
        break;
      }
    }
  });

  await connection.query(sql`
    CREATE TEMPORARY TABLE tmp_zyr_movie (
      id INT NOT NULL,
      slug TEXT
    );
  `);
  await connection.copyFrom(
    "COPY tmp_zyr_movie (id, slug) FROM STDIN",
    rows.map((row) => ({
      id: Number(row.id),
      slug: row.slug,
    })),
  );
  await connection.mutate(sql`
    UPDATE
      zyr_movie
    SET
      slug = tmp_zyr_movie.slug
    FROM
      tmp_zyr_movie
    WHERE
      tmp_zyr_movie.id = zyr_movie.id
      AND (
        tmp_zyr_movie.slug != zyr_movie.slug
        OR (tmp_zyr_movie.slug IS NULL) != (zyr_movie.slug IS NULL)
      )
  `);
}

async function initializeNewPeople(connection: Connection): Promise<void> {
  await connection.query(
    sql`CREATE TEMP TABLE tmp_zyr_person (LIKE zyr_person)`,
  );
  await connection.query(sql`
    INSERT INTO tmp_zyr_person (id) (
      SELECT
        id
      FROM
        wikidata_person
      WHERE
        wikidata_person.full_name_ru IS NOT NULL
    )
  `);
  await connection.query(
    sql`
      INSERT INTO zyr_person
      (
        SELECT
          *
        FROM
          tmp_zyr_person
      )
      ON CONFLICT (id)
        DO NOTHING
    `,
  );
  await connection.query(sql`DROP TABLE tmp_zyr_person`);
}

async function refreshPeopleCache(connection: Connection): Promise<void> {
  await connection.query(sql`REFRESH MATERIALIZED VIEW "graphql_person"`);
}

async function refreshPersonImageCache(connection: Connection): Promise<void> {
  await connection.query(sql`REFRESH MATERIALIZED VIEW "graphql_person_image"`);
}

async function fillPeopleSlugs(connection: Connection): Promise<void> {
  const rows = await connection.query<{
    id: number;
    slug: string | null;
    full_name_en: string | null;
  }>(sql`
    SELECT
      zyr_person.id as "id",
      zyr_person.slug as "slug",
      wikidata_person.full_name_en as "full_name_en"
    FROM
      zyr_person
    LEFT JOIN
      wikidata_person
      ON wikidata_person.id = zyr_person.id
  `);

  const slugToId = new Map<string, number>();
  const idToSlug = new Map<number, string>();

  rows.forEach((row) => {
    if (row.slug) {
      slugToId.set(row.slug, row.id);
      idToSlug.set(row.id, row.slug);
    }
  });

  rows.forEach((row) => {
    if (idToSlug.has(row.id)) {
      return;
    }

    const slugs: string[] = [];

    if (row.full_name_en) {
      slugs.push(slugify(row.full_name_en));
    }

    for (const slug of slugs) {
      if (!slugToId.has(slug)) {
        slugToId.set(slug, row.id);
        idToSlug.set(row.id, slug);
        // eslint-disable-next-line no-param-reassign
        row.slug = slug;
        break;
      }
    }
  });

  await connection.query(sql`
    CREATE TEMPORARY TABLE tmp_zyr_person (
      id INT NOT NULL,
      slug TEXT
    );
  `);
  await connection.copyFrom(
    "COPY tmp_zyr_person (id, slug) FROM STDIN",
    rows.map((row) => ({
      id: Number(row.id),
      slug: row.slug,
    })),
  );
  await connection.mutate(sql`
    UPDATE
      zyr_person
    SET
      slug = tmp_zyr_person.slug
    FROM
      tmp_zyr_person
    WHERE
      tmp_zyr_person.id = zyr_person.id
      AND (
        tmp_zyr_person.slug != zyr_person.slug
        OR (tmp_zyr_person.slug IS NULL) != (zyr_person.slug IS NULL)
      )
  `);
}

async function refreshFriendsCache(connection: Connection): Promise<void> {
  await connection.query(sql`REFRESH MATERIALIZED VIEW "graphql_friend"`);
}

async function refreshMovieRatingCache(connection: Connection): Promise<void> {
  await connection.query(sql`REFRESH MATERIALIZED VIEW "graphql_movie_top"`);
}

function slugify(str: string): string {
  return str
    .toString()
    .normalize("NFD")
    .replace(/[\u0300-\u036F]/g, "") // remove diacritics
    .toLowerCase()
    .replace(/\s+/g, "-") // spaces to dashes
    .replace(/&/g, "-and-") // ampersand to and
    .replace(/[^\w-]+/g, "") // remove non-words
    .replace(/--+/g, "-") // collapse multiple dashes
    .replace(/^-+/, "") // trim starting dash
    .replace(/-+$/, ""); // trim ending dash;
}
