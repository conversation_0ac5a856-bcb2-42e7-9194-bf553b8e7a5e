import WatchlistRepository from "../app/WatchlistRepository.js";
import Watchlist, { WatchlistItem } from "../domain/Watchlist.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresWatchlistRepository
  implements WatchlistRepository
{
  constructor(private pool: ConnectionPool) {}

  async find(accountId: string): Promise<Watchlist | null> {
    return this.pool.transaction(async (connection) => {
      const rawWatchlistItems = await connection.query<{
        movie_id: number;
        created_at: Date;
      }>(sql`
        SELECT
          movie_id,
          created_at
        FROM
          zyr_watchlist
        WHERE
          account_id = ${Number(accountId)}
      `);

      const watchlistItems: WatchlistItem[] = rawWatchlistItems.map(
        (rawWatchlistItem) => ({
          movieId: String(rawWatchlistItem.movie_id),
          timestamp: rawWatchlistItem.created_at,
        }),
      );

      return watchlistItems;
    });
  }

  async set(accountId: string, watchlist: Watchlist): Promise<void> {
    await this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_zyr_watchlist (LIKE zyr_watchlist)`,
      );
      await connection.copyFrom(
        "COPY tmp_zyr_watchlist (account_id, movie_id, created_at) FROM STDIN",
        watchlist.map((watchlistItem) => ({
          account_id: Number(accountId),
          movie_id: Number(watchlistItem.movieId),
          created_at: watchlistItem.timestamp,
        })),
      );

      await connection.mutate(
        sql`
          INSERT INTO zyr_watchlist
          (
            SELECT
              *
            FROM
              tmp_zyr_watchlist
            WHERE
              NOT EXISTS (
                SELECT
                FROM
                  zyr_watchlist
                WHERE
                  zyr_watchlist.movie_id = tmp_zyr_watchlist.movie_id
                  AND zyr_watchlist.account_id = tmp_zyr_watchlist.account_id
              )
          )
        `,
      );

      await connection.query(
        sql`
          INSERT INTO zyr_watchlist_deleted (account_id, movie_id, created_at, deleted_at)
          (
            SELECT
              zyr_watchlist.account_id as account_id,
              zyr_watchlist.movie_id as movie_id,
              zyr_watchlist.created_at as created_at,
              NOW() as deleted_at
            FROM
              zyr_watchlist
            WHERE
              zyr_watchlist.account_id = ${Number(accountId)}
              AND NOT EXISTS (
                SELECT
                FROM
                  tmp_zyr_watchlist
                WHERE
                  tmp_zyr_watchlist.movie_id = zyr_watchlist.movie_id
                  AND tmp_zyr_watchlist.account_id = zyr_watchlist.account_id
              )
          )
        `,
      );

      await connection.mutate(
        sql`
          DELETE FROM zyr_watchlist
          WHERE
            zyr_watchlist.account_id = ${Number(accountId)}
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_zyr_watchlist
              WHERE
                tmp_zyr_watchlist.movie_id = zyr_watchlist.movie_id
                AND tmp_zyr_watchlist.account_id = zyr_watchlist.account_id
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_zyr_watchlist`);
    });
  }
}
