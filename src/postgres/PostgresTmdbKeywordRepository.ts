import { Keyword, KeywordRepository } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbKeywordRepository
  implements KeywordRepository
{
  constructor(private pool: ConnectionPool) {}

  async findMany(ids: string[]): Promise<(Keyword | null)[]> {
    if (ids.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        name: string;
      }>(sql`
        SELECT
          tmdb_keyword.id as id,
          tmdb_keyword.name as name
        FROM
          tmdb_keyword
        WHERE
          id IN (${sql.raw(ids.map((id) => `${id}`).join(", "))})
      `);
      const genres: Keyword[] = rows.map((row) => ({
        id: String(row.id),
        name: row.name,
      }));
      const hash = new Map(genres.map((m) => [m.id, m]));

      return ids.map((id) => hash.get(id) ?? null);
    });
  }

  async setMany(keywords: Keyword[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_keyword (LIKE tmdb_keyword)`,
      );
      await connection.copyFrom(
        "COPY tmp_tmdb_keyword (id, name, created_at, updated_at) FROM STDIN",
        keywords.map((keyword) => ({
          id: Number(keyword.id),
          name: keyword.name,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO tmdb_keyword
          (SELECT * FROM tmp_tmdb_keyword)
          ON CONFLICT (id) DO NOTHING
        `,
      );
      await connection.query(sql`DROP TABLE tmp_tmdb_keyword`);
    });
  }
}
