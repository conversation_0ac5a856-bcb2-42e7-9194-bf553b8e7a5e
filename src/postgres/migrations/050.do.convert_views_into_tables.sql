DROP MATERIALIZED VIEW zyr_movie;
DROP MATERIALIZED VIEW zyr_movie_personal_stat;

CREATE TABLE zyr_movie (
  kinopoisk_url TEXT NOT NULL,
  title TEXT NOT NULL,
  duration_mins SMALLINT,
  year SMALLINT,
  directors_full_names TEXT[] NOT NULL,
  images_idxs INTEGER[] NOT NULL,
  images_variant_urls TEXT[] NOT NULL,
  images_widths INTEGER[] NOT NULL,
  images_heights INTEGER[] NOT NULL,
  ts_text TEXT NOT NULL,
  ts TSVECTOR NOT NULL,

  PRIMARY KEY (kinopoisk_url)
);

CREATE INDEX zyr_movie_title_trgm ON zyr_movie USING GIN (ts_text gin_trgm_ops);
CREATE INDEX zyr_movie_search_idx ON zyr_movie USING GIN (ts);

CREATE TABLE zyr_movie_personal_stat (
  user_kinopoisk_url TEXT NOT NULL,
  movie_kinopoisk_url TEXT NOT NULL,
  first_friend_timestamp TIMESTAMP,
  views BIGINT NOT NULL,
  trend BIGINT NOT NULL,
  raw_rating DOUBLE PRECISION,
  rating DOUBLE PRECISION,
  top_position BIGINT,
  user_mark INTEGER,
  user_mark_timestamp TIMESTAMP,
  user_seen BOOLEAN,

  PRIMARY KEY (user_kinopoisk_url, movie_kinopoisk_url)
);

CREATE INDEX zyr_movie_personal_stat_rating ON zyr_movie_personal_stat (rating);
