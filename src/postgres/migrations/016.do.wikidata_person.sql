CREATE TABLE wikidata_person (
  full_name TEXT NOT NULL,
  wikidata_url TEXT PRIMARY KEY
);

CREATE TABLE movie_director (
  person_wikidata_url TEXT NOT NULL,
  movie_kinopoisk_url TEXT NOT NULL,

  PRIMARY KEY (person_wikidata_url, movie_kinopoisk_url),
  CONSTRAINT director_wikidata_entity FOREIGN KEY (person_wikidata_url) REFERENCES wikidata_person (wikidata_url) ON DELETE CASCADE,
  CONSTRAINT director_movie_kinopoisk_url FOREIGN KEY (movie_kinopoisk_url) REFERENCES movie (kinopoisk_url) ON DELETE CASCADE
);

CREATE INDEX movie_director_movie_kinopoisk_url ON movie_director (movie_kinopoisk_url);
