CREATE VIEW zyr_movie AS (
  SELECT 
    movie.kinopoisk_url as kinopoisk_url,
    movie.title as title,
    movie.duration_mins as duration_mins,
    movie.year as year,
    director.full_names as directors_full_names,
    images.idxs as images_idxs,
    images.variant_urls as images_variant_urls,
    images.widths as images_widths,
    images.heights as images_heights,
    (movie.title || ' ' || movie.year) as ts_text,
    (to_tsvector('simple', movie.title || ' ' || movie.year)) as ts
  FROM
    movie
  LEFT JOIN LATERAL
    (
      SELECT ARRAY_AGG(wikidata_person.full_name) as full_names
      FROM movie_director
      JOIN wikidata_person ON
        wikidata_person.wikidata_url = movie_director.person_wikidata_url
      WHERE movie_director.movie_kinopoisk_url = movie.kinopoisk_url
      GROUP BY true
    ) director ON true
  LEFT JOIN LATERAL
    (
      SELECT
        ARRAY_AGG(movie_image.image_idx) as idxs,
        ARRAY_AGG(movie_image.variant_url) as variant_urls,
        ARRAY_AGG(movie_image.width) as widths,
        ARRAY_AGG(movie_image.height) as heights
      FROM
        movie_image
      WHERE
        movie_image.movie_kinopoisk_url = movie.kinopoisk_url
    ) images
    ON true
);

CREATE VIEW zyr_movie_personal_stat AS (
  SELECT
    movie_personal_stat.movie_kinopoisk_url as movie_kinopoisk_url,
    movie_personal_stat.user_kinopoisk_url as user_kinopoisk_url,
    movie_personal_stat.rating as rating,
    movie_personal_stat.views as views,
    movie_personal_stat.top_position as top_position,
    movie_personal_stat.friends_timestamps as friends_timestamps,
    movie_personal_stat.first_friend_timestamp as first_friend_timestamp,
    user_mark.mark as user_mark,
    user_mark.timestamp as user_mark_timestamp,
    (user_mark.movie_kinopoisk_url IS NOT NULL) as user_seen
  FROM
    movie_personal_stat
  LEFT JOIN
    user_mark
    ON user_mark.movie_kinopoisk_url = movie_personal_stat.movie_kinopoisk_url
    AND user_mark.user_kinopoisk_url = movie_personal_stat.user_kinopoisk_url
);
