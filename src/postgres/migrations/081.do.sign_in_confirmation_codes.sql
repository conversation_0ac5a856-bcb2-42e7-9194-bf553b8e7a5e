ALTER TABLE account_webauthn_credential DROP CONSTRAINT account_webauthn_credential_pkey;

ALTER TABLE account_webauthn_credential ADD CONSTRAINT account_webauthn_credential_pkey PRIMARY KEY (credential_id);

CREATE TABLE account_sign_in_confirmation_code (
  account_id INTEGER NOT NULL,
  code TEXT NOT NULL,
  credential_id TEXT,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (code),
  CONSTRAINT sign_in_confirmation_code_account_id FOREIGN KEY (account_id) REFERENCES account (id) ON DELETE CASCADE,
  CONSTRAINT sign_in_confirmation_code_credential_id FOREIGN KEY (credential_id) REFERENCES account_webauthn_credential (credential_id) ON DELETE CASCADE
);
