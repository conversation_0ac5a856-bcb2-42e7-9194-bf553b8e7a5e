CREATE MATERIALIZED VIEW zyr_movie AS (
  SELECT 
    movie.kinopoisk_url as kinopoisk_url,
    movie.title as title,
    movie.duration_mins as duration_mins,
    movie.year as year,
    director.full_names as directors_full_names,
    images.idxs as images_idxs,
    images.variant_urls as images_variant_urls,
    images.widths as images_widths,
    images.heights as images_heights,
    (movie.title || ' ' || movie.year) as ts_text,
    (to_tsvector('simple', movie.title || ' ' || movie.year)) as ts
  FROM
    movie
  LEFT JOIN LATERAL
    (
      SELECT ARRAY_AGG(wikidata_person.full_name) as full_names
      FROM movie_director
      JOIN wikidata_person ON
        wikidata_person.wikidata_url = movie_director.person_wikidata_url
      WHERE movie_director.movie_kinopoisk_url = movie.kinopoisk_url
      GROUP BY true
    ) director ON true
  LEFT JOIN LATERAL
    (
      SELECT
        ARRAY_AGG(movie_image.image_idx) as idxs,
        ARRAY_AGG(movie_image.variant_url) as variant_urls,
        ARRAY_AGG(movie_image.width) as widths,
        ARRAY_AGG(movie_image.height) as heights
      FROM
        movie_image
      WHERE
        movie_image.movie_kinopoisk_url = movie.kinopoisk_url
    ) images
    ON true
);

CREATE INDEX zyr_movie_kinopoisk_url ON zyr_movie (kinopoisk_url);

DROP INDEX movie_title_trgm;
CREATE INDEX zyr_movie_title_trgm ON zyr_movie USING GIN (ts_text gin_trgm_ops);

DROP INDEX movie_search_idx;
CREATE INDEX zyr_movie_search_idx ON zyr_movie USING GIN (ts);
