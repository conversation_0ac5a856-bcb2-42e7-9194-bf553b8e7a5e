DROP MATERIALIZED VIEW zyr_movie_personal_stat;

CREATE MATERIALIZED VIEW zyr_movie_personal_stat AS (
  SELECT
    account.kinopoisk_url as user_kinopoisk_url,
    friends_mark.movie_kinopoisk_url as movie_kinopoisk_url,
    friends_mark.first_mark_timestamp as first_friend_timestamp,
    friends_mark.views as views,
    (
      SELECT COUNT(*) as count
      FROM UNNEST(friends_mark.marks_timestamps) as t (timestamp)
      WHERE timestamp >= (current_date - 14)
        AND timestamp <= current_date
    ) as trend,
    CASE 
      WHEN (
        2 * 0.84 * SQRT(
          (
            (
              1 * 1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
              2 * 2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
              3 * 3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
              4 * 4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
              5 * 5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
              6 * 6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
              7 * 7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
              8 * 8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
              9 * 9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
              10 * 10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
            ) - POW(
              (
                1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
              ),
              2
            )
          ) / (friends_mark.marks_count + 10 + 1)
        )
      ) < 1 THEN (
        10 * (
          (
            1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
            2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
            3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
            4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
            5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
            6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
            7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
            8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
            9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
            10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
          ) - 0.84 * SQRT(
            (
              (
                1 * 1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                2 * 2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                3 * 3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                4 * 4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                5 * 5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                6 * 6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                7 * 7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                8 * 8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                9 * 9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                10 * 10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
              ) - POW(
                (
                  1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                  2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                  3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                  4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                  5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                  6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                  7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                  8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                  9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                  10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
                ),
                2
              )
            ) / (friends_mark.marks_count + 10 + 1)
          )
        )
      )
      ELSE NULL
    END as rating,
    row_number() OVER (
      PARTITION BY account.kinopoisk_url
      ORDER BY (
        CASE 
          WHEN (
            2 * 0.84 * SQRT(
              (
                (
                  1 * 1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                  2 * 2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                  3 * 3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                  4 * 4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                  5 * 5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                  6 * 6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                  7 * 7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                  8 * 8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                  9 * 9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                  10 * 10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
                ) - POW(
                  (
                    1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                    2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                    3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                    4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                    5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                    6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                    7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                    8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                    9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                    10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
                  ),
                  2
                )
              ) / (friends_mark.marks_count + 10 + 1)
            )
          ) < 1 THEN (
            10 * (
              (
                1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
              ) - 0.84 * SQRT(
                (
                  (
                    1 * 1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                    2 * 2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                    3 * 3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                    4 * 4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                    5 * 5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                    6 * 6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                    7 * 7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                    8 * 8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                    9 * 9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                    10 * 10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
                  ) - POW(
                    (
                      1 * (friends_mark.marks_histogram[1] + 1.0) / (friends_mark.marks_count + 10) +
                      2 * (friends_mark.marks_histogram[2] + 1.0) / (friends_mark.marks_count + 10) +
                      3 * (friends_mark.marks_histogram[3] + 1.0) / (friends_mark.marks_count + 10) +
                      4 * (friends_mark.marks_histogram[4] + 1.0) / (friends_mark.marks_count + 10) +
                      5 * (friends_mark.marks_histogram[5] + 1.0) / (friends_mark.marks_count + 10) +
                      6 * (friends_mark.marks_histogram[6] + 1.0) / (friends_mark.marks_count + 10) +
                      7 * (friends_mark.marks_histogram[7] + 1.0) / (friends_mark.marks_count + 10) +
                      8 * (friends_mark.marks_histogram[8] + 1.0) / (friends_mark.marks_count + 10) +
                      9 * (friends_mark.marks_histogram[9] + 1.0) / (friends_mark.marks_count + 10) +
                      10 * (friends_mark.marks_histogram[10] + 1.0) / (friends_mark.marks_count + 10)
                    ),
                    2
                  )
                ) / (friends_mark.marks_count + 10 + 1)
              )
            )
          )
          ELSE NULL
        END
      ) DESC NULLS LAST, friends_mark.movie_kinopoisk_url DESC
    ) as top_position,
    my_mark.mark as user_mark,
    my_mark.timestamp as user_mark_timestamp,
    (my_mark.movie_kinopoisk_url IS NOT NULL) as user_seen
  FROM
    account
  JOIN LATERAL
    (
      SELECT
        user_mark.movie_kinopoisk_url as movie_kinopoisk_url,
        COUNT(*) as views,
        ARRAY[
          COUNT(NULLIF(user_mark.mark = 1, false)),
          COUNT(NULLIF(user_mark.mark = 2, false)),
          COUNT(NULLIF(user_mark.mark = 3, false)),
          COUNT(NULLIF(user_mark.mark = 4, false)),
          COUNT(NULLIF(user_mark.mark = 5, false)),
          COUNT(NULLIF(user_mark.mark = 6, false)),
          COUNT(NULLIF(user_mark.mark = 7, false)),
          COUNT(NULLIF(user_mark.mark = 8, false)),
          COUNT(NULLIF(user_mark.mark = 9, false)),
          COUNT(NULLIF(user_mark.mark = 10, false))
        ] as marks_histogram,
        COUNT(NULLIF(user_mark.mark IS NOT NULL, false)) as marks_count,
        ARRAY_AGG(user_mark.timestamp) as marks_timestamps,
        MIN(user_mark.timestamp) as first_mark_timestamp
      FROM
        user_mark
      JOIN
        user_friend
        ON user_friend.user_kinopoisk_url = account.kinopoisk_url
        AND user_friend.friend_kinopoisk_url = user_mark.user_kinopoisk_url
      GROUP BY
        user_mark.movie_kinopoisk_url
    ) friends_mark
    ON true
  LEFT JOIN
    user_mark my_mark
    ON my_mark.movie_kinopoisk_url = friends_mark.movie_kinopoisk_url
    AND my_mark.user_kinopoisk_url = account.kinopoisk_url
);

CREATE INDEX zyr_movie_personal_stat_user_kinopoisk_url ON zyr_movie_personal_stat (user_kinopoisk_url);
CREATE INDEX zyr_movie_personal_stat_first_user_timestamp ON zyr_movie_personal_stat (first_friend_timestamp);
CREATE INDEX zyr_movie_personal_stat_rating ON zyr_movie_personal_stat (rating);
CREATE INDEX zyr_movie_personal_stat_trend ON zyr_movie_personal_stat (trend);
