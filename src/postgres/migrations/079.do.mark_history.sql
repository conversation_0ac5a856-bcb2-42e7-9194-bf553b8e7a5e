CREATE TABLE zyr_movie_mark_history (
  account_id INTEGER NOT NULL,
  movie_id INTEGER NOT NULL,
  mark INTEGER,
  timestamp TIMESTAMP NOT NULL,

  CONSTRAINT zyr_movie_mark_history_account_id FOREIGN KEY (account_id) REFERENCES account (id) ON DELETE CASCADE,
  CONSTRAINT zyr_movie_mark_history_movie_id FOREIGN KEY (movie_id) REFERENCES kinopoisk_movie (id) ON DELETE CASCADE
);

CREATE INDEX zyr_movie_mark_history_account_id_movie_id ON zyr_movie_mark_history (account_id, movie_id);

INSERT INTO zyr_movie_mark_history (
  SELECT
    account.id as account_id,
    kinopoisk_movie_mark.movie_id as movie_id,
    null as mark,
    kinopoisk_movie_mark.created_at as timestamp
  FROM
    account
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = account.kinopoisk_id

  UNION ALL

  SELECT
    account.id as account_id,
    kinopoisk_movie_mark.movie_id as movie_id,
    kinopoisk_movie_mark.mark as mark,
    kinopoisk_movie_mark.created_at as timestamp
  FROM
    account
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = account.kinopoisk_id
  WHERE
    kinopoisk_movie_mark.created_at = kinopoisk_movie_mark.updated_at

  UNION ALL

  SELECT
    account.id as account_id,
    kinopoisk_movie_mark.movie_id as movie_id,
    kinopoisk_movie_mark.mark as mark,
    kinopoisk_movie_mark.updated_at as timestamp
  FROM
    account
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = account.kinopoisk_id
  WHERE
    kinopoisk_movie_mark.created_at != kinopoisk_movie_mark.updated_at
);
