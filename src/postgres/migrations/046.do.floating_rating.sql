DROP MATERIALIZED VIEW zyr_movie_personal_stat;

CREATE FUNCTION lerp(a double precision, b double precision, t double precision)
  RETURNS double precision
  AS $$
    SELECT (1.0 - t) * a + b * t
  $$
  LANGUAGE SQL;

CREATE FUNCTION inverse_lerp(a double precision, b double precision, v double precision)
  RETURNS double precision
  AS $$
    SELECT (v - a) / (b - a)
  $$
  LANGUAGE SQL;

CREATE FUNCTION remap(imin double precision, imax double precision, omin double precision, omax double precision, v double precision)
  RETURNS double precision
  AS $$
    SELECT lerp(omin, omax, inverse_lerp(imin, imax, v))
  $$
  LANGUAGE SQL;

CREATE MATERIALIZED VIEW zyr_movie_personal_stat AS (
  SELECT
    account.kinopoisk_url as user_kinopoisk_url,
    friends_mark.movie_kinopoisk_url as movie_kinopoisk_url,
    friends_mark.first_mark_timestamp as first_friend_timestamp,
    friends_mark.views as views,
    (
      SELECT COUNT(*) as count
      FROM UNNEST(friends_mark.marks_timestamps) as t (timestamp)
      WHERE timestamp >= (current_date - 14)
        AND timestamp <= current_date
    ) as trend,
    CASE 
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 3.6
        THEN remap(3.6, MAX(bayesian_average_rating(friends_mark.marks_histogram, 0.84)) OVER (), 4, 5, bayesian_average_rating(friends_mark.marks_histogram, 0.84))
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 3.0
        THEN remap(3.0, 3.6, 3, 4, bayesian_average_rating(friends_mark.marks_histogram, 0.84))
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 2.25
        THEN remap(2.25, 3.0, 2, 3, bayesian_average_rating(friends_mark.marks_histogram, 0.84))
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 1.25
        THEN remap(1.25, 2.25, 1, 2, bayesian_average_rating(friends_mark.marks_histogram, 0.84))
      ELSE remap(0, 1.25, 0, 1, bayesian_average_rating(friends_mark.marks_histogram, 0.84))
    END as rating,
    row_number() OVER (
      PARTITION BY account.kinopoisk_url
      ORDER BY 
        bayesian_average_rating(friends_mark.marks_histogram, 0.84) DESC NULLS LAST,
        friends_mark.movie_kinopoisk_url DESC
    ) as top_position,
    my_mark.mark as user_mark,
    my_mark.timestamp as user_mark_timestamp,
    (my_mark.movie_kinopoisk_url IS NOT NULL) as user_seen
  FROM
    account
  JOIN LATERAL
    (
      SELECT
        user_mark.movie_kinopoisk_url as movie_kinopoisk_url,
        COUNT(*) as views,
        ARRAY[
          COUNT(NULLIF(user_mark.mark = 1, false)) + COUNT(NULLIF(user_mark.mark = 2, false)),
          COUNT(NULLIF(user_mark.mark = 3, false)) + COUNT(NULLIF(user_mark.mark = 4, false)),
          COUNT(NULLIF(user_mark.mark = 5, false)) + COUNT(NULLIF(user_mark.mark = 6, false)),
          COUNT(NULLIF(user_mark.mark = 7, false)) + COUNT(NULLIF(user_mark.mark = 8, false)),
          COUNT(NULLIF(user_mark.mark = 9, false)) + COUNT(NULLIF(user_mark.mark = 10, false))
        ] as marks_histogram,
        COUNT(NULLIF(user_mark.mark IS NOT NULL, false)) as marks_count,
        ARRAY_AGG(user_mark.timestamp) as marks_timestamps,
        MIN(user_mark.timestamp) as first_mark_timestamp
      FROM
        user_mark
      JOIN
        user_friend
        ON user_friend.user_kinopoisk_url = account.kinopoisk_url
        AND user_friend.friend_kinopoisk_url = user_mark.user_kinopoisk_url
      GROUP BY
        user_mark.movie_kinopoisk_url
    ) friends_mark
    ON true
  LEFT JOIN
    user_mark my_mark
    ON my_mark.movie_kinopoisk_url = friends_mark.movie_kinopoisk_url
    AND my_mark.user_kinopoisk_url = account.kinopoisk_url
);

CREATE INDEX zyr_movie_personal_stat_user_kinopoisk_url ON zyr_movie_personal_stat (user_kinopoisk_url);
CREATE INDEX zyr_movie_personal_stat_first_user_timestamp ON zyr_movie_personal_stat (first_friend_timestamp);
CREATE INDEX zyr_movie_personal_stat_rating ON zyr_movie_personal_stat (rating);
CREATE INDEX zyr_movie_personal_stat_trend ON zyr_movie_personal_stat (trend);
