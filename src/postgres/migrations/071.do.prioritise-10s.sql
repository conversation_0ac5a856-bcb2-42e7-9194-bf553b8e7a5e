DROP FUNCTION remap;
DROP FUNCTION lerp;
DROP FUNCTION inverse_lerp;
DROP FUNCTION bayesian_average_rating;
DROP FUNCTION bayesian_average_rating_interval_width;
DROP FUNCTION bayesian_average_rating_lower_bound;

CREATE FUNCTION bayesian_average_rating_interval_width(histogram numeric[], weights numeric[], z double precision)
  RETURNS double precision
  AS $$
    SELECT 
      2 * z * SQRT(
        (
          SUM(POW(sk, 2) * (nk + 1.0) / (n + k)) - 
          POW(SUM(sk * (nk + 1.0) / (n + k)), 2)
        ) / (
          n + k + 1
        )
      )
    FROM
      (
        SELECT
          c as nk,
          weights[row_number() OVER ()] as sk,
          sum(c) OVER () as n,
          cardinality(histogram) k
        FROM
          unnest(histogram) as c
      ) h
    GROUP BY
      n, k
  $$
  LANGUAGE SQL;

CREATE FUNCTION bayesian_average_rating_lower_bound(histogram numeric[], weights numeric[], z double precision)
  RETURNS double precision
  AS $$
    SELECT 
      SUM(sk * (nk + 1) / (n + k)) - z * SQRT((
        SUM(POW(sk, 2) * (nk + 1.0) / (n + k)) - 
        POW(SUM(sk * (nk + 1.0) / (n + k)), 2)
      ) / (n + k + 1))
    FROM
      (
        SELECT
          c as nk,
          weights[row_number() OVER ()] as sk,
          sum(c) OVER () as n,
          cardinality(histogram) k
        FROM
          unnest(histogram) as c
      ) h
    GROUP BY
      n, k
  $$
  LANGUAGE SQL;