ALTER TABLE zyr_movie_personal_stat DROP CONSTRAINT zyr_movie_personal_stat_pkey;
ALTER TABLE kinopoisk_friend DROP CONSTRAINT user_friend_pkey;
ALTER TABLE kinopoisk_friend DROP CONSTRAINT user_friend_friend_kinopoisk_url;
ALTER TABLE kinopoisk_friend DROP CONSTRAINT user_friend_user_kinopoisk_url;
ALTER TABLE kinopoisk_movie_mark DROP CONSTRAINT user_mark_pkey;
ALTER TABLE kinopoisk_movie_mark DROP CONSTRAINT user_mark_movie_kinopoisk_url;
ALTER TABLE kinopoisk_movie_mark DROP CONSTRAINT user_mark_user_kinopoisk_url;
ALTER TABLE kinopoisk_movie DROP CONSTRAINT movie_pkey;
ALTER TABLE kinopoisk_user DROP CONSTRAINT user_pkey;
ALTER TABLE zyr_movie DROP CONSTRAINT zyr_movie_pkey;
ALTER TABLE account DROP CONSTRAINT account_pkey;
DROP INDEX zyr_movie_kinopoisk_id_idx;

ALTER TABLE account ADD COLUMN id INTEGER;
ALTER TABLE account ADD COLUMN kinopoisk_id INTEGER;
UPDATE account SET id = substring(kinopoisk_url from 'https://www.kinopoisk.ru/user/(\d+)/')::INTEGER, kinopoisk_id = substring(kinopoisk_url from 'https://www.kinopoisk.ru/user/(\d+)/')::INTEGER;
ALTER TABLE account ALTER id SET NOT NULL;
ALTER TABLE account ALTER kinopoisk_id SET NOT NULL;

ALTER TABLE kinopoisk_user ADD COLUMN id INTEGER;
UPDATE kinopoisk_user SET id = substring(kinopoisk_url from 'https://www.kinopoisk.ru/user/(\d+)/')::INTEGER;
ALTER TABLE kinopoisk_user ALTER id SET NOT NULL;

ALTER TABLE kinopoisk_movie_mark ADD COLUMN user_id INTEGER;
ALTER TABLE kinopoisk_movie_mark ADD COLUMN movie_id INTEGER;
UPDATE kinopoisk_movie_mark
  SET user_id = substring(user_kinopoisk_url from 'https://www.kinopoisk.ru/user/(\d+)/')::INTEGER,
      movie_id = substring(movie_kinopoisk_url from 'https://www.kinopoisk.ru/(?:series|film)/(\d+)/')::INTEGER;
ALTER TABLE kinopoisk_movie_mark ALTER user_id SET NOT NULL;
ALTER TABLE kinopoisk_movie_mark ALTER movie_id SET NOT NULL;

ALTER TABLE kinopoisk_friend ADD COLUMN user_id INTEGER;
ALTER TABLE kinopoisk_friend ADD COLUMN friend_id INTEGER;
UPDATE kinopoisk_friend
  SET user_id = substring(user_kinopoisk_url from 'https://www.kinopoisk.ru/user/(\d+)/')::INTEGER,
      friend_id = substring(friend_kinopoisk_url from 'https://www.kinopoisk.ru/user/(\d+)/')::INTEGER;
ALTER TABLE kinopoisk_friend ALTER user_id SET NOT NULL;
ALTER TABLE kinopoisk_friend ALTER friend_id SET NOT NULL;

ALTER TABLE zyr_movie ADD COLUMN id INTEGER;
UPDATE zyr_movie SET id = kinopoisk_id;
ALTER TABLE zyr_movie ALTER id SET NOT NULL;

ALTER TABLE zyr_movie_personal_stat ADD COLUMN movie_id INTEGER;
ALTER TABLE zyr_movie_personal_stat ADD COLUMN account_id INTEGER;
UPDATE zyr_movie_personal_stat
  SET account_id = substring(user_kinopoisk_url from 'https://www.kinopoisk.ru/user/(\d+)/')::INTEGER,
      movie_id = substring(movie_kinopoisk_url from 'https://www.kinopoisk.ru/(?:series|film)/(\d+)/')::INTEGER;
ALTER TABLE zyr_movie_personal_stat ALTER movie_id SET NOT NULL;
ALTER TABLE zyr_movie_personal_stat ALTER account_id SET NOT NULL;

ALTER TABLE account ADD CONSTRAINT account_pkey PRIMARY KEY (id);
ALTER TABLE kinopoisk_movie ADD CONSTRAINT kinopoisk_movie_pkey PRIMARY KEY (id);
ALTER TABLE kinopoisk_user ADD CONSTRAINT kinopoisk_user_pkey PRIMARY KEY (id);
ALTER TABLE kinopoisk_movie_mark ADD CONSTRAINT kinopoisk_movie_mark_pkey PRIMARY KEY (movie_id, user_id);
ALTER TABLE zyr_movie ADD CONSTRAINT zyr_movie_pkey PRIMARY KEY (id);
ALTER TABLE zyr_movie_personal_stat ADD CONSTRAINT zyr_movie_personal_stat_pkey PRIMARY KEY (movie_id, account_id);
ALTER TABLE kinopoisk_friend ADD CONSTRAINT kinopoisk_friend_pkey PRIMARY KEY (user_id, friend_id);

ALTER TABLE kinopoisk_friend ADD CONSTRAINT kinopoisk_friend_mark_user_id FOREIGN KEY (user_id) REFERENCES kinopoisk_user (id) ON DELETE CASCADE;
ALTER TABLE kinopoisk_friend ADD CONSTRAINT kinopoisk_friend_mark_friend_id FOREIGN KEY (friend_id) REFERENCES kinopoisk_user (id) ON DELETE CASCADE;
ALTER TABLE kinopoisk_movie_mark ADD CONSTRAINT kinopoisk_movie_mark_movie_id FOREIGN KEY (movie_id) REFERENCES kinopoisk_movie (id) ON DELETE CASCADE;
ALTER TABLE kinopoisk_movie_mark ADD CONSTRAINT kinopoisk_movie_mark_user_id FOREIGN KEY (user_id) REFERENCES kinopoisk_user (id) ON DELETE CASCADE;
ALTER TABLE zyr_movie_personal_stat ADD CONSTRAINT zyr_movie_personal_stat_movie_id FOREIGN KEY (movie_id) REFERENCES zyr_movie (id) ON DELETE CASCADE;
ALTER TABLE zyr_movie_personal_stat ADD CONSTRAINT zyr_movie_personal_stat_account_id FOREIGN KEY (account_id) REFERENCES account (id) ON DELETE CASCADE;

ALTER TABLE account DROP COLUMN kinopoisk_url;
ALTER TABLE kinopoisk_movie DROP COLUMN kinopoisk_url;
ALTER TABLE kinopoisk_movie DROP COLUMN imdb_id;
ALTER TABLE kinopoisk_movie DROP COLUMN tmdb_id;
ALTER TABLE kinopoisk_movie DROP COLUMN duration_mins;
ALTER TABLE kinopoisk_movie DROP COLUMN wikidata_url;
ALTER TABLE kinopoisk_user DROP COLUMN kinopoisk_url;
ALTER TABLE kinopoisk_movie_mark DROP COLUMN user_kinopoisk_url;
ALTER TABLE kinopoisk_movie_mark DROP COLUMN movie_kinopoisk_url;
ALTER TABLE zyr_movie DROP COLUMN kinopoisk_id;
ALTER TABLE zyr_movie_personal_stat DROP COLUMN user_kinopoisk_url;
ALTER TABLE zyr_movie_personal_stat DROP COLUMN movie_kinopoisk_url;
