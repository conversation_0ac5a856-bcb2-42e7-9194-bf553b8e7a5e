CREATE TABLE account_webauthn_credential (
  account_id INTEGER NOT NULL,
  credential_id TEXT NOT NULL,
  credential_public_key TEXT NOT NULL,
  count BIGINT NOT NULL,
  credential_device_type TEXT NOT NULL,
  credential_backed_up <PERSON><PERSON><PERSON><PERSON>N NOT NULL,
  transports TEXT[],
  relying_party_id TEXT,

  PRIMARY KEY (account_id, credential_id),
  CONSTRAINT account_account_id FOREIGN KEY (account_id) REFERENCES account (id) ON DELETE CASCADE
);
