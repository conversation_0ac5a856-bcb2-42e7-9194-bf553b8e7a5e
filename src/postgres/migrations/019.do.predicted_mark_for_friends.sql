DROP MATERIALIZED VIEW user_predicted_mark;

CREATE MATERIALIZED VIEW user_predicted_mark AS (
  SELECT "user".kinopoisk_url as user_kinopoisk_url,
        friends_mark.movie_kinopoisk_url as movie_kinopoisk_url,
        friends_mark.predicted_mark as predicted_mark,
        friends_mark.marks_count as friends_marks_count,
        friends_mark.friends_marks as friends_marks,
        friends_mark.friends_timestamps as friends_timestamps
  FROM "user"
  JOIN LATERAL (
    SELECT user_mark.movie_kinopoisk_url as movie_kinopoisk_url,
          (
            1 * COUNT(NULLIF(user_mark.mark = 6, false)) +
            2 * COUNT(NULLIF(user_mark.mark = 7, false)) +
            5 * COUNT(NULLIF(user_mark.mark = 8, false)) +
            20 * COUNT(NULLIF(user_mark.mark = 9, false)) +
            50 * COUNT(NULLIF(user_mark.mark = 10, false))
          ) as predicted_mark,
          ARRAY_AGG(user_mark.mark) as friends_marks,
          ARRAY_AGG(user_mark.timestamp) as friends_timestamps,
          COUNT(user_mark.mark) as marks_count
    FROM user_mark
    JOIN user_friend ON
          user_friend.user_kinopoisk_url = "user".kinopoisk_url
      AND user_friend.friend_kinopoisk_url = user_mark.user_kinopoisk_url
    WHERE user_mark.mark IS NOT NULL
    GROUP BY user_mark.movie_kinopoisk_url
  ) friends_mark ON true
  JOIN movie ON movie.kinopoisk_url = friends_mark.movie_kinopoisk_url
);

CREATE INDEX user_predicted_mark_user_kinopoisk_url ON user_predicted_mark (user_kinopoisk_url);
