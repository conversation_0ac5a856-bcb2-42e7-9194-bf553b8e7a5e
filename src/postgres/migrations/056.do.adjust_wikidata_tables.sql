CREATE TABLE wikidata_movie (
  id INTEGER NOT NULL,
  title_en TEXT,
  title_ru TEXT,
  duration_mins INTEGER,
  kinopoisk_id INTEGER,
  imdb_id TEXT,
  tmdb_id INTEGER,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (id)
);
INSERT INTO wikidata_movie (id, duration_mins, kinopoisk_id, imdb_id, tmdb_id, created_at, updated_at) (
  SELECT
    substring(wikidata_url from 'http://www.wikidata.org/entity/Q(\d+)')::INTEGER as id,
    duration_mins,
    substring(kinopoisk_url from 'https://www.kinopoisk.ru/(?:series|film)/(\d+)/')::INTEGER as kinopoisk_id,
    imdb_id,
    tmdb_id::INTEGER as tmdb_id,
    NOW() as created_at,
    NOW() as updated_at
  FROM
    kinopoisk_movie
  WHERE
    wikidata_url IS NOT NULL
);

ALTER TABLE wikidata_person ADD COLUMN id INTEGER;
UPDATE wikidata_person SET id = substring(wikidata_url from 'http://www.wikidata.org/entity/Q(\d+)')::INTEGER;
ALTER TABLE wikidata_person ALTER id SET NOT NULL;

ALTER TABLE wikidata_person ADD COLUMN created_at TIMESTAMP;
ALTER TABLE wikidata_person ADD COLUMN updated_at TIMESTAMP;
UPDATE wikidata_person SET created_at = NOW(), updated_at = NOW();
ALTER TABLE wikidata_person ALTER created_at SET NOT NULL;
ALTER TABLE wikidata_person ALTER updated_at SET NOT NULL;

ALTER TABLE movie_director RENAME TO wikidata_movie_crew_member;

ALTER TABLE wikidata_movie_crew_member ADD COLUMN movie_id INTEGER;
INSERT INTO wikidata_movie_crew_member (movie_kinopoisk_url, person_wikidata_url, movie_id) (
  SELECT
    movie_kinopoisk_url,
    person_wikidata_url,
    substring(kinopoisk_movie.wikidata_url from 'http://www.wikidata.org/entity/Q(\d+)')::INTEGER as movie_id
  FROM
    wikidata_movie_crew_member
  JOIN
    kinopoisk_movie
    ON kinopoisk_movie.kinopoisk_url = movie_kinopoisk_url
) ON CONFLICT (movie_kinopoisk_url, person_wikidata_url) DO UPDATE SET movie_id = excluded.movie_id;
DELETE FROM wikidata_movie_crew_member WHERE movie_id IS NULL;
ALTER TABLE wikidata_movie_crew_member ALTER movie_id SET NOT NULL;

ALTER TABLE wikidata_movie_crew_member ADD COLUMN created_at TIMESTAMP;
ALTER TABLE wikidata_movie_crew_member ADD COLUMN updated_at TIMESTAMP;
UPDATE wikidata_movie_crew_member SET created_at = NOW(), updated_at = NOW();
ALTER TABLE wikidata_movie_crew_member ALTER created_at SET NOT NULL;
ALTER TABLE wikidata_movie_crew_member ALTER updated_at SET NOT NULL;

ALTER TABLE wikidata_movie_crew_member ADD COLUMN person_id INTEGER;
UPDATE wikidata_movie_crew_member SET person_id = substring(person_wikidata_url from 'http://www.wikidata.org/entity/Q(\d+)')::INTEGER;
ALTER TABLE wikidata_movie_crew_member ALTER person_id SET NOT NULL;

ALTER TABLE wikidata_movie_crew_member ADD COLUMN role TEXT;
UPDATE wikidata_movie_crew_member SET role = 'director';
ALTER TABLE wikidata_movie_crew_member ALTER role SET NOT NULL;

ALTER TABLE wikidata_movie_crew_member DROP CONSTRAINT movie_director_pkey;
ALTER TABLE wikidata_movie_crew_member DROP CONSTRAINT director_movie_kinopoisk_url;
ALTER TABLE wikidata_movie_crew_member DROP CONSTRAINT director_wikidata_entity;
ALTER TABLE wikidata_person DROP CONSTRAINT wikidata_person_pkey;

ALTER TABLE wikidata_person ADD CONSTRAINT wikidata_person_pkey PRIMARY KEY (id);
ALTER TABLE wikidata_movie_crew_member ADD CONSTRAINT wikidata_movie_crew_member_pkey PRIMARY KEY (movie_id, person_id, role);
ALTER TABLE wikidata_movie_crew_member ADD CONSTRAINT wikidata_movie_crew_member_movie_id FOREIGN KEY (movie_id) REFERENCES wikidata_movie (id);
ALTER TABLE wikidata_movie_crew_member ADD CONSTRAINT wikidata_movie_crew_member_person_id FOREIGN KEY (person_id) REFERENCES wikidata_person (id);
ALTER TABLE wikidata_movie_crew_member DROP COLUMN person_wikidata_url;
ALTER TABLE wikidata_movie_crew_member DROP COLUMN movie_kinopoisk_url;

ALTER TABLE wikidata_person DROP COLUMN wikidata_url;
