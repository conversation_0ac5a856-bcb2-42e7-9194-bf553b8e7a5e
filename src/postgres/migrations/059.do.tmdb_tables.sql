CREATE TABLE tmdb_movie (
  id INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (id)
);
INSERT INTO tmdb_movie (id, created_at, updated_at) (
  SELECT
    tmdb_id as id,
    created_at,
    updated_at
  FROM
    wikidata_movie
  WHERE
    tmdb_id IS NOT NULL
);

ALTER TABLE movie_image ADD COLUMN created_at TIMESTAMP;
ALTER TABLE movie_image ADD COLUMN updated_at TIMESTAMP;
UPDATE movie_image SET created_at = NOW(), updated_at = NOW();
ALTER TABLE movie_image ALTER created_at SET NOT NULL;
ALTER TABLE movie_image ALTER updated_at SET NOT NULL;

ALTER TABLE movie_image ADD COLUMN movie_id INTEGER;
INSERT INTO movie_image (movie_kinopoisk_url, image_idx, variant_url, width, height, created_at, updated_at, movie_id) (
  SELECT
    movie_kinopoisk_url,
    image_idx,
    variant_url,
    width,
    height,
    movie_image.created_at as created_at,
    movie_image.updated_at as updated_at,
    wikidata_movie.tmdb_id as movie_id
  FROM
    movie_image
  LEFT JOIN
    wikidata_movie
    ON wikidata_movie.kinopoisk_id = substring(movie_kinopoisk_url from 'https://www.kinopoisk.ru/(?:series|film)/(\d+)/')::INTEGER
) ON CONFLICT (movie_kinopoisk_url, image_idx, variant_url) DO UPDATE SET movie_id = excluded.movie_id;
ALTER TABLE movie_image ALTER movie_id SET NOT NULL;
ALTER TABLE movie_image ADD CONSTRAINT tmdb_movie_image_movie_id FOREIGN KEY (movie_id) REFERENCES tmdb_movie (id);
ALTER TABLE movie_image DROP CONSTRAINT movie_image_pkey;
ALTER TABLE movie_image DROP CONSTRAINT movie_image_movie_kinopoisk_url;
ALTER TABLE movie_image ADD CONSTRAINT tmdb_movie_image_pkey PRIMARY KEY (movie_id, image_idx, variant_url);
ALTER TABLE movie_image DROP COLUMN movie_kinopoisk_url;

ALTER TABLE movie_image RENAME COLUMN image_idx TO "order";
ALTER TABLE movie_image RENAME COLUMN variant_url TO "url";
ALTER TABLE movie_image RENAME TO tmdb_movie_image;

CREATE TABLE image (
  source_url TEXT NOT NULL,
  url TEXT NOT NULL,
  width INTEGER NOT NULL,
  height INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (source_url)
);
INSERT INTO image (source_url, url, width, height, created_at, updated_at) (
  SELECT
    url as source_url,
    url,
    width,
    height,
    tmdb_movie_image.created_at as created_at,
    tmdb_movie_image.updated_at as updated_at
  FROM
    tmdb_movie_image
);
