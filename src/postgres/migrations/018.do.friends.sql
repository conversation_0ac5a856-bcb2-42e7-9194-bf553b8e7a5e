CREATE TABLE user_friend (
  user_kinopoisk_url TEXT NOT NULL,
  friend_kinopoisk_url TEXT NOT NULL,

  PRIMARY KEY (user_kinopoisk_url, friend_kinopoisk_url),
  CONSTRAINT user_friend_user_kinopoisk_url FOREIGN KEY (user_kinopoisk_url) REFERENCES "user" (kinopoisk_url) ON DELETE CASCADE,
  CONSTRAINT user_friend_friend_kinopoisk_url FOREIGN KEY (friend_kinopoisk_url) REFERENCES "user" (kinopoisk_url) ON DELETE CASCADE
);
