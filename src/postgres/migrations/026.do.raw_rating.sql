DROP MATERIALIZED VIEW movie_personal_stat;

CREATE MATERIALIZED VIEW movie_personal_stat AS (
  SELECT account.kinopoisk_url as user_kinopoisk_url,
         friends_mark.movie_kinopoisk_url as movie_kinopoisk_url,
         friends_mark.rating as rating,
         friends_mark.likes as likes,
         friends_mark.views as views,
         friends_mark.marks_timestamps as friends_timestamps,
         friends_mark.first_mark_timestamp as first_friend_timestamp,
         row_number() OVER (PARTITION BY account.kinopoisk_url ORDER BY friends_mark.rating DESC, friends_mark.movie_kinopoisk_url DESC) as top_position
  FROM account
  JOIN LATERAL (
    SELECT user_mark.movie_kinopoisk_url as movie_kinopoisk_url,
          (
            1 +
            1 * COUNT(NULLIF(user_mark.mark = 6, false)) +
            2 * COUNT(NULLIF(user_mark.mark = 7, false)) +
            5 * COUNT(NULLIF(user_mark.mark = 8, false)) +
            20 * COUNT(NULLIF(user_mark.mark = 9, false)) +
            50 * COUNT(NULLIF(user_mark.mark = 10, false))
          ) as rating,
          COUNT(*) as views,
          COUNT(NULLIF(user_mark.mark = 9 OR user_mark.mark = 10, false)) as likes,
          ARRAY_AGG(user_mark.timestamp) as marks_timestamps,
          MIN(user_mark.timestamp) as first_mark_timestamp
    FROM user_mark
    JOIN user_friend ON
          user_friend.user_kinopoisk_url = account.kinopoisk_url
      AND user_friend.friend_kinopoisk_url = user_mark.user_kinopoisk_url
    GROUP BY user_mark.movie_kinopoisk_url
  ) friends_mark ON true
);

CREATE INDEX movie_personal_stat_user_kinopoisk_url ON movie_personal_stat (user_kinopoisk_url);
CREATE INDEX movie_personal_stat_first_user_timestamp ON movie_personal_stat (first_friend_timestamp);
CREATE INDEX movie_personal_stat_rating ON movie_personal_stat (rating);
