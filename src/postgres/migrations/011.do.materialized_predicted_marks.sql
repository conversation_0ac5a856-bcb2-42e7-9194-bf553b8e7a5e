DROP VIEW user_predicted_mark;

SET random_page_cost = 1;

CREATE MATERIALIZED VIEW user_predicted_mark AS (
  SELECT "user".kinopoisk_url as user_kinopoisk_url,
        friends_mark.movie_kinopoisk_url as movie_kinopoisk_url,
        friends_mark.predicted_mark as predicted_mark,
        friends_mark.marks_count as friends_marks_count,
        friends_mark.friends_marks as friends_marks
  FROM "user"
  JOIN LATERAL (
    SELECT user_mark.movie_kinopoisk_url as movie_kinopoisk_url,
          percentile_disc(0.5) WITHIN GROUP (ORDER BY user_mark.mark) as predicted_mark,
          ARRAY_AGG(user_mark.mark) as friends_marks,
          COUNT(user_mark.mark) as marks_count
    FROM user_mark
    WHERE user_mark.user_kinopoisk_url != "user".kinopoisk_url AND user_mark.mark IS NOT NULL
    GROUP BY user_mark.movie_kinopoisk_url
    HAVING COUNT(user_mark.mark) > 2
  ) friends_mark ON true
);

CREATE INDEX user_predicted_mark_user_kinopoisk_url ON user_predicted_mark (user_kinopoisk_url);
