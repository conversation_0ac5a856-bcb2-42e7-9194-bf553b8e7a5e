DROP MATERIALIZED VIEW zyr_movie_personal_stat;
DROP FUNCTION bayesian_average_rating;
DROP FUNCTION bayesian_average_rating_interval_width;
DROP FUNCTION bayesian_average_rating_lower_bound;

CREATE FUNCTION bayesian_average_rating_interval_width(histogram numeric[], z double precision)
  RETURNS double precision
  AS $$
    SELECT 
      2 * z * SQRT(
        (
          SUM(POW(sk, 2) * (nk + 1.0) / (n + k)) - 
          POW(SUM(sk * (nk + 1.0) / (n + k)), 2)
        ) / (
          n + k + 1
        )
      )
    FROM
      (
        SELECT
          c as nk,
          row_number() OVER () as sk,
          sum(c) OVER () as n,
          cardinality(histogram) k
        FROM
          unnest(histogram) as c
      ) h
    GROUP BY
      n, k
  $$
  LANGUAGE SQL;

CREATE FUNCTION bayesian_average_rating_lower_bound(histogram numeric[], z double precision)
  RETURNS double precision
  AS $$
    SELECT 
      SUM(sk * (nk + 1) / (n + k)) - z * SQRT((
        SUM(POW(sk, 2) * (nk + 1.0) / (n + k)) - 
        POW(SUM(sk * (nk + 1.0) / (n + k)), 2)
      ) / (n + k + 1))
    FROM
      (
        SELECT
          c as nk,
          row_number() OVER () as sk,
          sum(c) OVER () as n,
          cardinality(histogram) k
        FROM
          unnest(histogram) as c
      ) h
    GROUP BY
      n, k
  $$
  LANGUAGE SQL;

CREATE FUNCTION bayesian_average_rating(histogram numeric[], z double precision)
  RETURNS double precision
  AS $$
    SELECT
      CASE 
        WHEN
          bayesian_average_rating_interval_width(histogram, z) < 1
        THEN
          bayesian_average_rating_lower_bound(histogram, z)
        ELSE NULL
      END
  $$
  LANGUAGE SQL;

CREATE MATERIALIZED VIEW zyr_movie_personal_stat AS (
  SELECT
    account.kinopoisk_url as user_kinopoisk_url,
    friends_mark.movie_kinopoisk_url as movie_kinopoisk_url,
    friends_mark.first_mark_timestamp as first_friend_timestamp,
    friends_mark.views as views,
    (
      SELECT COUNT(*) as count
      FROM UNNEST(friends_mark.marks_timestamps) as t (timestamp)
      WHERE timestamp >= (current_date - 14)
        AND timestamp <= current_date
    ) as trend,
    CASE 
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) IS NULL THEN NULL
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 7.0 THEN 5
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 6.5 THEN 4
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 6.0 THEN 3
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 5.3 THEN 2
      WHEN bayesian_average_rating(friends_mark.marks_histogram, 0.84) >= 4.6 THEN 1
      ELSE 0
    END as rating,
    row_number() OVER (
      PARTITION BY account.kinopoisk_url
      ORDER BY 
        bayesian_average_rating(friends_mark.marks_histogram, 0.84) DESC NULLS LAST,
        friends_mark.movie_kinopoisk_url DESC
    ) as top_position,
    my_mark.mark as user_mark,
    my_mark.timestamp as user_mark_timestamp,
    (my_mark.movie_kinopoisk_url IS NOT NULL) as user_seen
  FROM
    account
  JOIN LATERAL
    (
      SELECT
        user_mark.movie_kinopoisk_url as movie_kinopoisk_url,
        COUNT(*) as views,
        ARRAY[
          COUNT(NULLIF(user_mark.mark = 1, false)),
          COUNT(NULLIF(user_mark.mark = 2, false)),
          COUNT(NULLIF(user_mark.mark = 3, false)),
          COUNT(NULLIF(user_mark.mark = 4, false)),
          COUNT(NULLIF(user_mark.mark = 5, false)),
          COUNT(NULLIF(user_mark.mark = 6, false)),
          COUNT(NULLIF(user_mark.mark = 7, false)),
          COUNT(NULLIF(user_mark.mark = 8, false)),
          COUNT(NULLIF(user_mark.mark = 9, false)),
          COUNT(NULLIF(user_mark.mark = 10, false))
        ] as marks_histogram,
        COUNT(NULLIF(user_mark.mark IS NOT NULL, false)) as marks_count,
        ARRAY_AGG(user_mark.timestamp) as marks_timestamps,
        MIN(user_mark.timestamp) as first_mark_timestamp
      FROM
        user_mark
      JOIN
        user_friend
        ON user_friend.user_kinopoisk_url = account.kinopoisk_url
        AND user_friend.friend_kinopoisk_url = user_mark.user_kinopoisk_url
      GROUP BY
        user_mark.movie_kinopoisk_url
    ) friends_mark
    ON true
  LEFT JOIN
    user_mark my_mark
    ON my_mark.movie_kinopoisk_url = friends_mark.movie_kinopoisk_url
    AND my_mark.user_kinopoisk_url = account.kinopoisk_url
);

CREATE INDEX zyr_movie_personal_stat_user_kinopoisk_url ON zyr_movie_personal_stat (user_kinopoisk_url);
CREATE INDEX zyr_movie_personal_stat_first_user_timestamp ON zyr_movie_personal_stat (first_friend_timestamp);
CREATE INDEX zyr_movie_personal_stat_rating ON zyr_movie_personal_stat (rating);
CREATE INDEX zyr_movie_personal_stat_trend ON zyr_movie_personal_stat (trend);
