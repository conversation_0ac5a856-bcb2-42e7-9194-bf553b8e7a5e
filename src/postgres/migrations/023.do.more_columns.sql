DROP MATERIALIZED VIEW user_predicted_mark;

CREATE MATERIALIZED VIEW movie_personal_stat AS (
  SELECT account.kinopoisk_url as user_kinopoisk_url,
         friends_mark.movie_kinopoisk_url as movie_kinopoisk_url,
         CASE WHEN friends_mark.rating > 70 THEN 3
              WHEN friends_mark.rating > 60 THEN 2
              WHEN friends_mark.rating > 40 THEN 1
              ELSE 0
         END as rating,
         friends_mark.likes as likes,
         friends_mark.views as views,
         friends_mark.marks_timestamps as friends_timestamps,
         row_number() OVER (PARTITION BY account.kinopoisk_url ORDER BY friends_mark.rating DESC, friends_mark.movie_kinopoisk_url DESC) as top_position
  FROM account
  JOIN LATERAL (
    SELECT user_mark.movie_kinopoisk_url as movie_kinopoisk_url,
          100 * LOG(
            1 +
            1 * COUNT(NULLIF(user_mark.mark = 6, false)) +
            2 * COUNT(NULLIF(user_mark.mark = 7, false)) +
            5 * COUNT(NULLIF(user_mark.mark = 8, false)) +
            20 * COUNT(NULLIF(user_mark.mark = 9, false)) +
            50 * COUNT(NULLIF(user_mark.mark = 10, false))
          ) / MAX(LOG(
            1 +
            1 * COUNT(NULLIF(user_mark.mark = 6, false)) +
            2 * COUNT(NULLIF(user_mark.mark = 7, false)) +
            5 * COUNT(NULLIF(user_mark.mark = 8, false)) +
            20 * COUNT(NULLIF(user_mark.mark = 9, false)) +
            50 * COUNT(NULLIF(user_mark.mark = 10, false))
          )) OVER () as rating,
          COUNT(user_mark.mark) as views,
          COUNT(NULLIF(user_mark.mark = 9 OR user_mark.mark = 10, false)) as likes,
          ARRAY_AGG(user_mark.timestamp) as marks_timestamps
    FROM user_mark
    JOIN user_friend ON
          user_friend.user_kinopoisk_url = account.kinopoisk_url
      AND user_friend.friend_kinopoisk_url = user_mark.user_kinopoisk_url
    GROUP BY user_mark.movie_kinopoisk_url
  ) friends_mark ON true
  JOIN movie ON movie.kinopoisk_url = friends_mark.movie_kinopoisk_url
);

CREATE INDEX movie_personal_stat_user_kinopoisk_url ON movie_personal_stat (user_kinopoisk_url);
