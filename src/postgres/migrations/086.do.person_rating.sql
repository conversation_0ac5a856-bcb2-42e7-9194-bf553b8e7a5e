CREATE TABLE zyr_person_personal_stat (
  account_id INTEGER NOT NULL,
  person_id INTEGER NOT NULL,
  top_position_all_time BIGINT,

  PRIMARY KEY (account_id, person_id),
  CONSTRAINT zyr_person_personal_stat_account_id FOREIGN KEY (account_id) REFERENCES account (id) ON DELETE CASCADE,
  CONSTRAINT zyr_person_personal_stat_person_id FOREIGN KEY (person_id) REFERENCES zyr_person (id) ON DELETE CASCADE
);

CREATE INDEX zyr_person_personal_stat_top_position_all_time ON zyr_person_personal_stat (top_position_all_time ASC);
