CREATE TABLE wikidata_genre (
  id INTEGER NOT NULL,
  label_en TEXT,
  label_ru TEXT,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (id)
);

CREATE TABLE wikidata_movie_genre (
  movie_id INTEGER NOT NULL,
  genre_id INTEGER NOT NULL,
  "order" INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (movie_id, genre_id)
);
