CREATE TABLE tmdb_genre (
  id INTEGER NOT NULL,
  name TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (id)
);

CREATE TABLE tmdb_movie_genre (
  movie_id INTEGER NOT NULL,
  genre_id INTEGER NOT NULL,
  "order" INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (movie_id, genre_id),
  CONSTRAINT tmdb_movie_genre_movie_id FOREIGN KEY (movie_id) REFERENCES tmdb_movie (id) ON DELETE CASCADE,
  CONSTRAINT tmdb_movie_genre_genre_id FOREIGN KEY (genre_id) REFERENCES tmdb_genre (id) ON DELETE CASCADE
);
