CREATE TABLE tmdb_keyword (
  id INTEGER NOT NULL,
  name TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (id)
);

CREATE TABLE tmdb_movie_keyword (
  movie_id INTEGER NOT NULL,
  keyword_id INTEGER NOT NULL,
  "order" INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (movie_id, keyword_id),
  CONSTRAINT tmdb_movie_keyword_movie_id FOREIGN KEY (movie_id) REFERENCES tmdb_movie (id) ON DELETE CASCADE,
  CONSTRAINT tmdb_movie_keyword_keyword_id FOREIGN KEY (keyword_id) REFERENCES tmdb_keyword (id) ON DELETE CASCADE
);
