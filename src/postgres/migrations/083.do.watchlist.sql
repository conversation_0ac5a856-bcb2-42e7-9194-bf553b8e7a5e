CREATE TABLE zyr_watchlist (
  account_id INTEGER NOT NULL,
  movie_id INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,

  PRIMARY KEY (account_id, movie_id),
  CONSTRAINT zyr_watchlist_account_id FOREIGN KEY (account_id) REFERENCES account (id) ON DELETE CASCADE,
  CONSTRAINT zyr_watchlist_movie_id FOREIGN KEY (movie_id) REFERENCES kinopoisk_movie (id) ON DELETE CASCADE
);

CREATE TABLE zyr_watchlist_deleted (
  account_id INTEGER NOT NULL,
  movie_id INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NOT NULL,

  CONSTRAINT zyr_watchlist_account_id FOREIGN KEY (account_id) REFERENCES account (id) ON DELETE CASCADE,
  CONSTRAINT zyr_watchlist_movie_id FOREIGN KEY (movie_id) REFERENCES kinopoisk_movie (id) ON DELETE CASCADE
);

ALTER TABLE zyr_movie_personal_stat ADD COLUMN watchlist_timestamp TIMESTAMP;
