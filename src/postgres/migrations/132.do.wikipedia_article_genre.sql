CREATE TABLE wikipedia_article_genre (
  article_slug TEXT NOT NULL,
  article_lang TEXT NOT NULL,
  genre_slug TEXT NOT NULL,
  genre_lang TEXT NOT NULL,
  "order" INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (article_slug, article_lang, genre_slug, genre_lang),
  CONSTRAINT wikipedia_article_genre_article_fkey 
    FOREIGN KEY (article_slug, article_lang) 
    REFERENCES wikipedia_article (slug, lang) 
    ON DELETE CASCADE
);

CREATE INDEX wikipedia_article_genre_article_idx 
  ON wikipedia_article_genre (article_slug, article_lang);
CREATE INDEX wikipedia_article_genre_genre_idx 
  ON wikipedia_article_genre (genre_slug, genre_lang);
