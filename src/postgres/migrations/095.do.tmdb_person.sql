CREATE TABLE tmdb_person (
  id INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (id)
);

CREATE TABLE tmdb_person_image (
  person_id INTEGER NOT NULL,
  "order" INTEGER NOT NULL,
  url TEXT NOT NULL,
  height INTEGER NOT NULL,
  width INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  PRIMARY KEY (person_id, "order", url),
  CONSTRAINT tmdb_person_image_person_id FOREIGN KEY (person_id) REFERENCES tmdb_person (id) ON DELETE CASCADE
);
