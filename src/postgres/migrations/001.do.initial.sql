CREATE TABLE movie (
  kinopoisk_url TEXT PRIMARY KEY,
  title TEXT NOT NULL
);

CREATE TABLE "user" (
  kinopoisk_url TEXT PRIMARY KEY
);

CREATE TABLE user_mark (
  user_kinopoisk_url TEXT NOT NULL,
  movie_kinopoisk_url TEXT NOT NULL,
  mark INTEGER NOT NULL,

  PRIMARY KEY (user_kinopoisk_url, movie_kinopoisk_url),
  CONSTRAINT user_mark_user_kinopoisk_url FOREIGN KEY (user_kinopoisk_url) REFERENCES "user" (kinopoisk_url) ON DELETE CASCADE,
  CONSTRAINT user_mark_movie_kinopoisk_url FOREIGN KEY (movie_kinopoisk_url) REFERENCES movie (kinopoisk_url) ON DELETE CASCADE
);
