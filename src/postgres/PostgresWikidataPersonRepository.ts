import { Person, PersonRepository } from "../jobs/PullWikidataMoviesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresWikidataPersonRepository
  implements PersonRepository
{
  constructor(private pool: ConnectionPool) {}

  async findAll(): Promise<Person[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        full_name_en: string | null;
        full_name_ru: string | null;
        tmdb_id: string | null;
        wikipedia_en_slug: string | null;
        wikipedia_ru_slug: string | null;
      }>(sql`
        SELECT
          id,
          full_name_en,
          full_name_ru,
          tmdb_id,
          wikipedia_en_slug,
          wikipedia_ru_slug
        FROM
          wikidata_person
      `);
      const people: Person[] = rows.map((row) => ({
        id: String(row.id),
        fullName: {
          en: row.full_name_en ?? undefined,
          ru: row.full_name_ru ?? undefined,
        },
        tmdbId: row.tmdb_id ? String(row.tmdb_id) : undefined,
        wikipediaSlugs: {
          en: row.wikipedia_en_slug ?? undefined,
          ru: row.wikipedia_ru_slug ?? undefined,
        },
      }));

      return people;
    });
  }

  async findMany(ids: string[]): Promise<(Person | null)[]> {
    if (ids.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        full_name_en: string | null;
        full_name_ru: string | null;
        tmdb_id: string | null;
        wikipedia_en_slug: string | null;
        wikipedia_ru_slug: string | null;
      }>(sql`
        SELECT
          id,
          full_name_en,
          full_name_ru,
          tmdb_id,
          wikipedia_en_slug,
          wikipedia_ru_slug
        FROM
          wikidata_person
        WHERE
          id IN (${sql.raw(ids.map((id) => `${id}`).join(", "))})
      `);
      const people: Person[] = rows.map((row) => ({
        id: String(row.id),
        fullName: {
          en: row.full_name_en ?? undefined,
          ru: row.full_name_ru ?? undefined,
        },
        tmdbId: row.tmdb_id ? String(row.tmdb_id) : undefined,
        wikipediaSlugs: {
          en: row.wikipedia_en_slug ?? undefined,
          ru: row.wikipedia_ru_slug ?? undefined,
        },
      }));
      const hash = new Map(people.map((p) => [p.id, p]));

      return ids.map((id) => hash.get(id) ?? null);
    });
  }

  async setMany(people: Person[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_wikidata_person (LIKE wikidata_person)`,
      );
      await connection.copyFrom(
        "COPY tmp_wikidata_person (id, full_name_en, full_name_ru, tmdb_id, wikipedia_en_slug, wikipedia_ru_slug, created_at, updated_at) FROM STDIN",
        people.map((person) => ({
          id: Number(person.id),
          full_name_en: person.fullName.en,
          full_name_ru: person.fullName.ru,
          tmdb_id: person.tmdbId == null ? null : Number(person.tmdbId),
          wikipedia_en_slug: person.wikipediaSlugs.en,
          wikipedia_ru_slug: person.wikipediaSlugs.ru,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO wikidata_person
          (SELECT * FROM tmp_wikidata_person)
          ON CONFLICT (id)
            DO UPDATE SET full_name_en = excluded.full_name_en,
                          full_name_ru = excluded.full_name_ru,
                          tmdb_id = excluded.tmdb_id,
                          updated_at = excluded.updated_at,
                          wikipedia_en_slug = excluded.wikipedia_en_slug,
                          wikipedia_ru_slug = excluded.wikipedia_ru_slug
            WHERE (
              wikidata_person.full_name_en,
              wikidata_person.full_name_ru,
              wikidata_person.tmdb_id,
              wikidata_person.wikipedia_en_slug,
              wikidata_person.wikipedia_ru_slug
            ) IS DISTINCT FROM (
              excluded.full_name_en,
              excluded.full_name_ru,
              excluded.tmdb_id,
              excluded.wikipedia_en_slug,
              excluded.wikipedia_ru_slug
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_wikidata_person`);
    });
  }
}
