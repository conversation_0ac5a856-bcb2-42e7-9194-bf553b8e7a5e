import { Movie, MovieRepository } from "../jobs/PullWikidataMoviesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresWikidataMovieRepository
  implements MovieRepository
{
  constructor(private pool: ConnectionPool) {}

  async findAll(): Promise<Movie[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        title_en: string | null;
        title_ru: string | null;
        duration_mins: number | null;
        kinopoisk_id: number | null;
        imdb_id: string | null;
        tmdb_id: number | null;
        wikipedia_en_slug: string | null;
        wikipedia_ru_slug: string | null;
        directors_ids: number[] | null;
        directors_orders: (number | null)[] | null;
        genres_ids: number[] | null;
        genres_orders: (number | null)[] | null;
      }>(sql`
        SELECT
          id,
          title_en,
          title_ru,
          duration_mins,
          kinopoisk_id,
          imdb_id,
          tmdb_id,
          wikipedia_en_slug,
          wikipedia_ru_slug,
          director.ids as directors_ids,
          director.orders as directors_orders,
          genre.ids as genres_ids,
          genre.orders as genres_orders
        FROM
          wikidata_movie
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(wikidata_movie_crew_member.person_id) as ids,
              ARRAY_AGG(wikidata_movie_crew_member.order) as orders
            FROM wikidata_movie_crew_member
            WHERE wikidata_movie_crew_member.movie_id = wikidata_movie.id
            GROUP BY wikidata_movie_crew_member.movie_id
          ) director ON true
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(wikidata_movie_genre.genre_id) as ids,
              ARRAY_AGG(wikidata_movie_genre.order) as orders
            FROM wikidata_movie_genre
            WHERE wikidata_movie_genre.movie_id = wikidata_movie.id
            GROUP BY wikidata_movie_genre.movie_id
          ) genre ON true
      `);
      const movies = rows.map(
        (row): Movie => ({
          id: String(row.id),
          title: {
            en: row.title_en ?? undefined,
            ru: row.title_ru ?? undefined,
          },
          kinopoiskId: row.kinopoisk_id ? String(row.kinopoisk_id) : undefined,
          directors:
            row.directors_ids
              ?.map((id, index) => ({
                id: String(id),
                order: row.directors_orders?.[index],
              }))
              .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
              .map((x) => ({
                id: x.id,
              })) ?? [],
          duration:
            typeof row.duration_mins === "number"
              ? { minutes: row.duration_mins }
              : undefined,
          genres:
            row.genres_ids
              ?.map((id, index) => ({
                id: String(id),
                order: row.genres_orders?.[index],
              }))
              .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
              .map((x) => ({
                id: x.id,
              })) ?? [],
          imdbId: row.imdb_id ?? undefined,
          tmdbId: row.tmdb_id ? String(row.tmdb_id) : undefined,
          wikipediaSlugs: {
            en: row.wikipedia_en_slug ?? undefined,
            ru: row.wikipedia_ru_slug ?? undefined,
          },
        }),
      );

      return movies;
    });
  }

  async findMany(wikidataIds: string[]): Promise<(Movie | null)[]> {
    if (wikidataIds.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        title_en: string | null;
        title_ru: string | null;
        duration_mins: number | null;
        kinopoisk_id: number | null;
        imdb_id: string | null;
        tmdb_id: number | null;
        wikipedia_en_slug: string | null;
        wikipedia_ru_slug: string | null;
        directors_ids: number[] | null;
        directors_orders: (number | null)[] | null;
        genres_ids: number[] | null;
        genres_orders: (number | null)[] | null;
      }>(sql`
        SELECT
          id,
          title_en,
          title_ru,
          duration_mins,
          kinopoisk_id,
          imdb_id,
          tmdb_id,
          wikipedia_en_slug,
          wikipedia_ru_slug,
          director.ids as directors_ids,
          director.orders as directors_orders,
          genre.ids as genres_ids,
          genre.orders as genres_orders
        FROM
          wikidata_movie
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(wikidata_movie_crew_member.person_id) as ids,
              ARRAY_AGG(wikidata_movie_crew_member.order) as orders
            FROM wikidata_movie_crew_member
            WHERE wikidata_movie_crew_member.movie_id = wikidata_movie.id
            GROUP BY wikidata_movie_crew_member.movie_id
          ) director ON true
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(wikidata_movie_genre.genre_id) as ids,
              ARRAY_AGG(wikidata_movie_genre.order) as orders
            FROM wikidata_movie_genre
            WHERE wikidata_movie_genre.movie_id = wikidata_movie.id
            GROUP BY wikidata_movie_genre.movie_id
          ) genre ON true
        WHERE
          id IN (${sql.raw(wikidataIds.map((id) => `${id}`).join(", "))})
      `);
      const movies = rows.map(
        (row): Movie => ({
          id: String(row.id),
          title: {
            en: row.title_en ?? undefined,
            ru: row.title_ru ?? undefined,
          },
          kinopoiskId: row.kinopoisk_id ? String(row.kinopoisk_id) : undefined,
          directors:
            row.directors_ids
              ?.map((id, index) => ({
                id: String(id),
                order: row.directors_orders?.[index],
              }))
              .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
              .map((x) => ({
                id: x.id,
              })) ?? [],
          duration:
            typeof row.duration_mins === "number"
              ? { minutes: row.duration_mins }
              : undefined,
          genres:
            row.genres_ids
              ?.map((id, index) => ({
                id: String(id),
                order: row.genres_orders?.[index],
              }))
              .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
              .map((x) => ({
                id: x.id,
              })) ?? [],
          imdbId: row.imdb_id ?? undefined,
          tmdbId: row.tmdb_id ? String(row.tmdb_id) : undefined,
          wikipediaSlugs: {
            en: row.wikipedia_en_slug ?? undefined,
            ru: row.wikipedia_ru_slug ?? undefined,
          },
        }),
      );
      const hash = new Map(movies.map((p) => [p.id, p]));

      return wikidataIds.map((id) => hash.get(id) ?? null);
    });
  }

  async setMany(movies: Movie[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_movie (LIKE wikidata_movie)`,
      );
      await connection.query(
        sql`CREATE TEMP TABLE tmp_wikidata_movie_crew_member (LIKE wikidata_movie_crew_member)`,
      );
      await connection.query(
        sql`CREATE TEMP TABLE tmp_wikidata_movie_genre (LIKE wikidata_movie_genre)`,
      );
      await connection.copyFrom(
        `COPY tmp_wikidata_movie_crew_member (person_id, movie_id, role, "order", created_at, updated_at) FROM STDIN`,
        movies
          .map((movie) =>
            movie.directors.map((person, index) => ({
              person_id: Number(person.id),
              movie_id: Number(movie.id),
              role: "director",
              order: index,
              created_at: new Date(),
              updated_at: new Date(),
            })),
          )
          .reduce((acc, x) => [...acc, ...x], []),
      );
      await connection.copyFrom(
        `COPY tmp_wikidata_movie_genre (genre_id, movie_id, "order", created_at, updated_at) FROM STDIN`,
        movies
          .map((movie) =>
            movie.genres.map((genre, index) => ({
              person_id: Number(genre.id),
              movie_id: Number(movie.id),
              order: index,
              created_at: new Date(),
              updated_at: new Date(),
            })),
          )
          .reduce((acc, x) => [...acc, ...x], []),
      );
      await connection.copyFrom(
        "COPY tmp_movie (id, title_en, title_ru, kinopoisk_id, imdb_id, tmdb_id, duration_mins, wikipedia_en_slug, wikipedia_ru_slug, created_at, updated_at) FROM STDIN",
        movies.map((movie) => ({
          id: Number(movie.id),
          title_en: movie.title.en,
          title_ru: movie.title.ru,
          kinopoisk_id:
            movie.kinopoiskId == null ? null : Number(movie.kinopoiskId),
          imdb_id: movie.imdbId,
          tmbd_id: movie.tmdbId == null ? null : Number(movie.tmdbId),
          duration_mins: movie.duration?.minutes,
          wikipedia_en_slug: movie.wikipediaSlugs.en,
          wikipedia_ru_slug: movie.wikipediaSlugs.ru,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO wikidata_movie (SELECT * FROM tmp_movie)
          ON CONFLICT (id) DO UPDATE SET
            title_en = excluded.title_en,
            title_ru = excluded.title_ru,
            kinopoisk_id = excluded.kinopoisk_id,
            imdb_id = excluded.imdb_id,
            tmdb_id = excluded.tmdb_id,
            duration_mins = excluded.duration_mins,
            wikipedia_en_slug = excluded.wikipedia_en_slug,
            wikipedia_ru_slug = excluded.wikipedia_ru_slug,
            updated_at = excluded.updated_at
          WHERE
            (wikidata_movie.title_en, wikidata_movie.title_ru, wikidata_movie.kinopoisk_id, wikidata_movie.imdb_id, wikidata_movie.tmdb_id, wikidata_movie.duration_mins, wikidata_movie.wikipedia_en_slug, wikidata_movie.wikipedia_ru_slug) IS DISTINCT FROM (excluded.title_en, excluded.title_ru, excluded.kinopoisk_id, excluded.imdb_id, excluded.tmdb_id, excluded.duration_mins, excluded.wikipedia_en_slug, excluded.wikipedia_ru_slug)
        `,
      );
      await connection.query(
        sql`
          INSERT INTO wikidata_movie_crew_member
          (SELECT * FROM tmp_wikidata_movie_crew_member)
          ON CONFLICT (movie_id, person_id, role) DO UPDATE SET
            "order" = excluded.order
          WHERE
            (wikidata_movie_crew_member.order) IS DISTINCT FROM (excluded.order)
        `,
      );
      await connection.query(
        sql`
          INSERT INTO wikidata_movie_genre
          (SELECT * FROM tmp_wikidata_movie_genre)
          ON CONFLICT (movie_id, genre_id) DO UPDATE SET
            "order" = excluded.order
          WHERE
            (wikidata_movie_genre.order) IS DISTINCT FROM (excluded.order)
        `,
      );
      await connection.query(
        sql`
          DELETE FROM
            wikidata_movie_crew_member
          WHERE
            EXISTS (
              SELECT
              FROM
                tmp_movie
              WHERE
                tmp_movie.id = wikidata_movie_crew_member.movie_id
            )
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_wikidata_movie_crew_member
              WHERE
                tmp_wikidata_movie_crew_member.movie_id = wikidata_movie_crew_member.movie_id
                AND tmp_wikidata_movie_crew_member.person_id = wikidata_movie_crew_member.person_id
                AND tmp_wikidata_movie_crew_member.role = wikidata_movie_crew_member.role
            )
        `,
      );
      await connection.query(
        sql`
          DELETE FROM
            wikidata_movie_genre
          WHERE
            EXISTS (
              SELECT
              FROM
                tmp_movie
              WHERE
                tmp_movie.id = wikidata_movie_genre.movie_id
            )
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_wikidata_movie_genre
              WHERE
                tmp_wikidata_movie_genre.movie_id = wikidata_movie_genre.movie_id
                AND tmp_wikidata_movie_genre.genre_id = wikidata_movie_genre.genre_id
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_movie`);
      await connection.query(sql`DROP TABLE tmp_wikidata_movie_crew_member`);
      await connection.query(sql`DROP TABLE tmp_wikidata_movie_genre`);
    });
  }
}
