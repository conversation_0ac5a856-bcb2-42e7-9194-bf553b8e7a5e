import SQL, { SQLStatement } from "sql-template-strings";

class RawValue {
  value: string;

  constructor(value: string) {
    this.value = value;
  }
}

function sql(
  strings: TemplateStringsArray,
  ...values: unknown[]
): SQLStatement {
  return strings.reduce(
    (statement, string, index) => {
      if (index > values.length - 1) {
        return statement.append(string);
      }

      const value = values[index];

      if (value instanceof RawValue) {
        return statement.append(string).append(value.value);
      }

      if (value instanceof SQLStatement) {
        return statement.append(string).append(value);
      }

      return statement.append(string).append(SQL`${value}`);
    },
    SQL``,
  );
}

sql.raw = (value: string): RawValue => {
  return new RawValue(value);
};

export default sql;
