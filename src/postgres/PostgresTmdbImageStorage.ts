import path from "node:path";
import { Readable } from "node:stream";
import streamPromises from "node:stream/promises";
import fs from "fs";
import { filetypename as filetype } from "magic-bytes.js";
import md5 from "md5";

import {
  DownloadInput,
  ImageStorage,
  StorageImage,
} from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbImageStorage implements ImageStorage {
  constructor(
    private pool: ConnectionPool,
    private destDir: string,
  ) {}

  async upload(stream: Readable, input: DownloadInput): Promise<StorageImage> {
    const filename = `${md5(input.sourceUrl).slice(0, 14)}.jpg`;
    const filepath = path.join(this.destDir, filename);
    const file = fs.createWriteStream(filepath);

    try {
      await streamPromises.pipeline(stream, file);
    } catch (error) {
      await fs.promises.rm(filepath);
      throw error;
    }

    const possibleTypes = filetype(fs.readFileSync(filepath));

    if (!possibleTypes.includes("jpg")) {
      await fs.promises.rm(filepath);
      throw new TypeError("Not a JPEG file");
    }

    const image: StorageImage = {
      sourceUrl: input.sourceUrl,
      height: input.height,
      width: input.width,
      url: `/img/${filename}`,
    };

    await this.setMany([image]);

    return image;
  }

  async findAll(): Promise<StorageImage[]> {
    return this.pool.transaction(async (connection) => {
      const rawImages = await connection.query<{
        source_url: string;
        url: string;
        height: number;
        width: number;
      }>(
        sql`
          SELECT
            source_url,
            url,
            height,
            width
          FROM
            image
        `,
      );
      const images: StorageImage[] = rawImages.map((i) => ({
        sourceUrl: i.source_url,
        url: i.url,
        height: i.height,
        width: i.width,
      }));

      return images;
    });
  }

  async findMany(sourceUrls: string[]): Promise<(StorageImage | null)[]> {
    if (sourceUrls.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rawImages = await connection.query<{
        source_url: string;
        url: string;
        height: number;
        width: number;
      }>(
        sql`
          SELECT
            source_url,
            url,
            height,
            width
          FROM
            image
          WHERE
            source_url IN (${sql.raw(
              sourceUrls.map((id) => `'${id}'`).join(", "),
            )})
        `,
      );
      const images: StorageImage[] = rawImages.map((i) => ({
        sourceUrl: i.source_url,
        url: i.url,
        height: i.height,
        width: i.width,
      }));
      const hash = new Map(images.map((m) => [m.sourceUrl, m]));

      return sourceUrls.map((url) => hash.get(url) ?? null);
    });
  }

  async deleteMany(images: StorageImage[]): Promise<void> {
    if (images.length === 0) {
      return;
    }

    await this.pool.transaction(async (connection) => {
      await connection.query(sql`CREATE TEMP TABLE tmp_image (LIKE image)`);
      await connection.copyFrom(
        "COPY tmp_image (source_url, url, height, width, created_at, updated_at) FROM STDIN",
        images.map((image) => ({
          source_url: image.sourceUrl,
          url: image.url,
          height: image.height,
          width: image.width,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.mutate(
        sql`
          DELETE FROM
            image
          WHERE
            EXISTS (
              SELECT FROM
                tmp_image
              WHERE
                tmp_image.source_url = image.source_url
                AND tmp_image.url = image.url
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_image`);
    });

    for (const image of images) {
      const filepath = path.join(this.destDir, image.url.replace("/img/", ""));

      if (fs.existsSync(filepath)) {
        await fs.promises.rm(filepath);
      }
    }
  }

  async findBroken(): Promise<StorageImage[]> {
    const images = await this.findAll();
    const broken: StorageImage[] = [];

    for (const image of images) {
      const filename = `${md5(image.sourceUrl).slice(0, 14)}.jpg`;
      const filepath = path.join(this.destDir, filename);

      if (!fs.existsSync(filepath)) {
        broken.push(image);
      }
    }

    return broken;
  }

  private async setMany(images: StorageImage[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(sql`CREATE TEMP TABLE tmp_image (LIKE image)`);
      await connection.copyFrom(
        "COPY tmp_image (source_url, url, height, width, created_at, updated_at) FROM STDIN",
        images.map((image) => ({
          source_url: image.sourceUrl,
          url: image.url,
          height: image.height,
          width: image.width,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO image
          (SELECT * FROM tmp_image)
          ON CONFLICT (source_url)
            DO UPDATE SET url = excluded.url,
                          height = excluded.height,
                          width = excluded.width,
                          updated_at = excluded.updated_at
            WHERE (image.url, image.height, image.width) IS DISTINCT FROM (excluded.url, excluded.height, excluded.width)
        `,
      );
      await connection.query(
        sql`
          DELETE FROM
            image
          WHERE
            image.source_url = image.url
            AND EXISTS (
              SELECT FROM
                tmp_image
              WHERE
                tmp_image.source_url != image.source_url
                AND tmp_image.url = image.url
            )
        `,
      );
      await connection.query(sql`DROP TABLE tmp_image`);
    });
  }
}
