import InviteRepository, { Invite } from "../app/InviteRepository.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresInviteRepository implements InviteRepository {
  constructor(private pool: ConnectionPool) {}

  async set(invite: Invite): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.mutate(sql`
        INSERT INTO zyr_invite (kinopoisk_url, email, name, created_at, updated_at)
        VALUES (${invite.kinopoiskUrl}, ${invite.email}, ${
          invite.name
        }, ${new Date()}, ${new Date()})
      `);
    });
  }
}
