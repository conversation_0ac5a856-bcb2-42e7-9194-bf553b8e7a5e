import { Logline, LoglineRepository } from "../jobs/ComposeLoglinesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresArticleRepository implements LoglineRepository {
  constructor(private pool: ConnectionPool) {}

  async set(logline: Logline): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.mutate(
        sql`
          INSERT INTO
            llm_logline (
              wikipedia_lang,
              wikipedia_slug,
              logline,
              model,
              prompt,
              "system",
              options,
              created_at
            )
          VALUES
            (
              ${logline.wikipedia.lang},
              ${logline.wikipedia.slug},
              ${logline.logline},
              ${logline.model},
              ${logline.prompt},
              ${logline.system},
              ${JSON.stringify(logline.options)}::jsonb,
              ${logline.createdAt.toISOString()}
            )
        `,
      );
    });
  }
}
