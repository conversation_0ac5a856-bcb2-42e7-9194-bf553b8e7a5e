import { JobQueue } from "../jobs/PullWikidataMoviesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresWikidataJobQueue implements JobQueue {
  constructor(private pool: ConnectionPool) {}

  async getJobsQueue(): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
      }>(sql`
        SELECT
          id
        FROM
          kinopoisk_movie
      `);

      return rows.map((row) => String(row.id));
    });
  }
}
