import { JobQueue } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbJobQueue implements JobQueue {
  constructor(private pool: ConnectionPool) {}

  async getMovieJobQueue(): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        tmdb_id: number;
      }>(sql`
        SELECT
          tmdb_id
        FROM
          wikidata_movie
        WHERE
          tmdb_id IS NOT NULL
      `);

      return rows.map((row) => String(row.tmdb_id));
    });
  }

  async getPeopleJobQueue(): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        tmdb_id: number;
      }>(sql`
        SELECT
          tmdb_id
        FROM
          wikidata_person
        WHERE
          tmdb_id IS NOT NULL
      `);

      return rows.map((row) => String(row.tmdb_id));
    });
  }
}
