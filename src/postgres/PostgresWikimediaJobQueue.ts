import {
  JobQueue,
  WikipediaArticleRef,
} from "../jobs/PullWikipediaArticlesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresWikimediaJobQueue implements JobQueue {
  constructor(private pool: ConnectionPool) {}

  async getJobsQueue(): Promise<WikipediaArticleRef[]> {
    return this.pool.transaction(async (connection) => {
      const refs = await connection.query<{
        lang: "en" | "ru";
        slug: string;
      }>(
        sql`
          SELECT
            'en' as "lang",
            wikidata_movie.wikipedia_en_slug as "slug"
          FROM
            wikidata_movie
          WHERE
            wikidata_movie.wikipedia_en_slug IS NOT NULL

          UNION ALL

          SELECT
            'ru' as "lang",
            wikidata_movie.wikipedia_ru_slug as "slug"
          FROM
            wikidata_movie
          WHERE
            wikidata_movie.wikipedia_ru_slug IS NOT NULL
        `,
      );

      return refs;
    });
  }
}
