import { stringify as csvStringify } from "csv-stringify/sync";
import pg from "pg";
import pgCopyStream from "pg-copy-streams";
import * as stream from "stream";
import * as util from "util";

const pipelineP = util.promisify(stream.pipeline);

interface QueryConfig {
  text: string;
  values?: unknown[] | undefined;
}

export default class Connection {
  private pgClient: pg.ClientBase;

  constructor(pgClient: pg.ClientBase) {
    this.pgClient = pgClient;
  }

  async mutate(sql: string | QueryConfig): Promise<number> {
    const queryResult = await this.pgClient.query(sql);

    return queryResult.rowCount;
  }

  async query<T extends pg.QueryResultRow = Record<string, unknown>>(
    sql: string | QueryConfig,
  ): Promise<T[]> {
    const queryResult = await this.pgClient.query<T>(sql);

    return queryResult.rows;
  }

  async copyFrom(
    queryText: string,
    rows: Record<string, unknown>[],
  ): Promise<void> {
    await pipelineP(
      stream.Readable.from(toTSV(rows)),
      this.pgClient.query(
        pgCopyStream.from(
          queryText.replace("FROM STDIN", "FROM STDIN DELIMITER ',' CSV"),
        ),
      ) as stream.Writable,
    );
  }
}

function toTSV(items: Record<string, unknown>[]): string {
  return csvStringify(
    items.map((mark) => Object.values(mark)),
    {
      cast: {
        boolean: (value) => (value === true ? "TRUE" : "FALSE"),
        date: (value) => value.toISOString(),
      },
    },
  );
}
