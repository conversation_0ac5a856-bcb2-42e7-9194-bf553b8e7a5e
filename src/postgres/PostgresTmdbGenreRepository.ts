import { Genre, GenreRepository } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbGenreRepository implements GenreRepository {
  constructor(private pool: ConnectionPool) {}

  async findMany(ids: string[]): Promise<(Genre | null)[]> {
    if (ids.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        name: string;
      }>(sql`
        SELECT
          tmdb_genre.id as id,
          tmdb_genre.name as name
        FROM
          tmdb_genre
        WHERE
          id IN (${sql.raw(ids.map((id) => `${id}`).join(", "))})
      `);
      const genres: Genre[] = rows.map((row) => ({
        id: String(row.id),
        name: row.name,
      }));
      const hash = new Map(genres.map((m) => [m.id, m]));

      return ids.map((id) => hash.get(id) ?? null);
    });
  }

  async setMany(genres: Genre[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_genre (LIKE tmdb_genre)`,
      );
      await connection.copyFrom(
        "COPY tmp_tmdb_genre (id, name, created_at, updated_at) FROM STDIN",
        genres.map((genre) => ({
          id: Number(genre.id),
          name: genre.name,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO tmdb_genre
          (SELECT * FROM tmp_tmdb_genre)
          ON CONFLICT (id) DO NOTHING
        `,
      );
      await connection.query(sql`DROP TABLE tmp_tmdb_genre`);
    });
  }
}
