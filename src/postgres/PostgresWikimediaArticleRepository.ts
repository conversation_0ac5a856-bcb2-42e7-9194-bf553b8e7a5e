import {
  Article,
  ArticleRef,
  ArticleRepository,
} from "../jobs/PullWikipediaArticlesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresWikimediaArticleRepository
  implements ArticleRepository
{
  constructor(private pool: ConnectionPool) {}

  async findAllRefs(): Promise<ArticleRef[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        lang: "en" | "ru";
        slug: string;
      }>(
        sql`
          SELECT
            wikipedia_article.lang as "lang",
            wikipedia_article.slug as "slug"
          FROM
            wikipedia_article
        `,
      );

      return rows.map(
        (row): ArticleRef => ({
          lang: row.lang,
          slug: row.slug,
        }),
      );
    });
  }

  async findManyRefs(refs: ArticleRef[]): Promise<(ArticleRef | null)[]> {
    if (refs.length === 0) {
      return [];
    }

    if (refs.length > 1000) {
      let result: (ArticleRef | null)[] = [];

      for (const chunk of chunks(refs, 1000)) {
        result = result.concat(await this.findManyRefs(chunk));
      }

      return result;
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        lang: "en" | "ru";
        slug: string;
      }>(
        sql`
          SELECT
            wikipedia_article.lang as "lang",
            wikipedia_article.slug as "slug"
          FROM
            wikipedia_article
          WHERE
        `.append(
          refs
            .map(
              (ref) =>
                sql`wikipedia_article.lang = ${ref.lang} AND wikipedia_article.slug = ${ref.slug}`,
            )
            .reduce((acc, statement, index) =>
              index === 0
                ? acc.append(statement)
                : acc.append(" OR ").append(statement),
            ),
        ),
      );
      const articles = rows.map(
        (row): ArticleRef => ({
          lang: row.lang,
          slug: row.slug,
        }),
      );
      const articlesMap = new Map(
        articles.map((article) => [
          `${article.lang}::${article.slug}`,
          article,
        ]),
      );

      return refs.map(
        (ref) => articlesMap.get(`${ref.lang}::${ref.slug}`) ?? null,
      );
    });
  }

  async findMany(refs: ArticleRef[]): Promise<(Article | null)[]> {
    if (refs.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        lang: "en" | "ru";
        slug: string;
        html: string;
        plot: string | null;
        created_at: Date;
        updated_at: Date;
      }>(
        sql`
          SELECT
            wikipedia_article.lang as "lang",
            wikipedia_article.slug as "slug",
            wikipedia_article.html as "html",
            wikipedia_article.plot as "plot",
            wikipedia_article.created_at as "created_at",
            wikipedia_article.updated_at as "updated_at"
          FROM
            wikipedia_article
          WHERE
        `.append(
          refs
            .map(
              (ref) =>
                sql`wikipedia_article.lang = ${ref.lang} AND wikipedia_article.slug = ${ref.slug}`,
            )
            .reduce((acc, statement, index) =>
              index === 0
                ? acc.append(statement)
                : acc.append(" OR ").append(statement),
            ),
        ),
      );

      const genreRows = await connection.query<{
        article_lang: "en" | "ru";
        article_slug: string;
        genre_lang: "en" | "ru";
        genre_slug: string;
        order: number;
      }>(
        sql`
          SELECT
            wikipedia_article_genre.article_lang as "article_lang",
            wikipedia_article_genre.article_slug as "article_slug",
            wikipedia_article_genre.genre_lang as "genre_lang",
            wikipedia_article_genre.genre_slug as "genre_slug",
            wikipedia_article_genre."order" as "order"
          FROM
            wikipedia_article_genre
          WHERE
        `.append(
          refs
            .map(
              (ref) =>
                sql`wikipedia_article_genre.article_lang = ${ref.lang} AND wikipedia_article_genre.article_slug = ${ref.slug}`,
            )
            .reduce((acc, statement, index) =>
              index === 0
                ? acc.append(statement)
                : acc.append(" OR ").append(statement),
            ),
        ).append(sql`
          ORDER BY wikipedia_article_genre."order" ASC
        `),
      );

      // Group genres by article
      const genresByArticle = new Map<
        string,
        { lang: "en" | "ru"; slug: string }[]
      >();
      genreRows.forEach((genreRow) => {
        const key = `${genreRow.article_lang}::${genreRow.article_slug}`;
        if (!genresByArticle.has(key)) {
          genresByArticle.set(key, []);
        }
        genresByArticle.get(key)!.push({
          lang: genreRow.genre_lang,
          slug: genreRow.genre_slug,
        });
      });

      const articles = rows.map(
        (row): Article => ({
          lang: row.lang,
          slug: row.slug,
          html: row.html,
          plot: row.plot,
          genres: genresByArticle.get(`${row.lang}::${row.slug}`) ?? [],
          createdAt: row.created_at,
          updatedAt: row.updated_at,
        }),
      );
      const articlesMap = new Map(
        articles.map((article) => [
          `${article.lang}::${article.slug}`,
          article,
        ]),
      );

      return refs.map(
        (ref) => articlesMap.get(`${ref.lang}::${ref.slug}`) ?? null,
      );
    });
  }

  async set(article: Article): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.mutate(
        sql`
          INSERT INTO
            wikipedia_article (lang, slug, html, plot, created_at, updated_at)
          VALUES
            (${article.lang}, ${article.slug}, ${article.html}, ${
              article.plot
            }, ${article.createdAt.toISOString()}, ${article.updatedAt.toISOString()})
          ON CONFLICT (lang, slug)
            DO UPDATE SET html = excluded.html,
                          plot = excluded.plot,
                          created_at = excluded.created_at,
                          updated_at = excluded.updated_at
        `,
      );

      await connection.mutate(
        sql`
          DELETE FROM wikipedia_article_genre
          WHERE article_slug = ${article.slug} AND article_lang = ${article.lang}
        `,
      );

      if (article.genres.length > 0) {
        await connection.query(
          sql`CREATE TEMP TABLE tmp_wikipedia_article_genre (LIKE wikipedia_article_genre)`,
        );

        await connection.copyFrom(
          'COPY tmp_wikipedia_article_genre (article_slug, article_lang, genre_slug, genre_lang, "order", created_at, updated_at) FROM STDIN',
          article.genres.map((genre, index) => ({
            article_slug: article.slug,
            article_lang: article.lang,
            genre_slug: genre.slug,
            genre_lang: genre.lang,
            order: index + 1,
            created_at: article.updatedAt,
            updated_at: article.updatedAt,
          })),
        );

        await connection.query(sql`
          INSERT INTO wikipedia_article_genre
          (SELECT * FROM tmp_wikipedia_article_genre)
        `);

        await connection.query(sql`DROP TABLE tmp_wikipedia_article_genre`);
      }
    });
  }
}

function chunks<T>(list: T[], size: number): T[][] {
  const cnks: T[][] = [];
  let chunk: T[] = [];

  list.forEach((item, index) => {
    chunk.push(item);

    if (chunk.length === size || index === list.length - 1) {
      cnks.push(chunk);
      chunk = [];
    }
  });

  return cnks;
}
