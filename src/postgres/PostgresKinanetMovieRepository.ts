import { Movie, MovieRepository } from "../kinanet/KinanetMovieAssociator.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresKinanetMovieRepository implements MovieRepository {
  constructor(private pool: ConnectionPool) {}

  async findAll(): Promise<Movie[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        titles: (string | null)[];
        directors: (string | null)[] | null;
        year: number;
      }>(sql`
        SELECT DISTINCT ON (kinopoisk_movie.id)
          kinopoisk_movie.id as id,
          ARRAY[
            kinopoisk_movie.title,
            wikidata_movie.title_en,
            wikidata_movie.title_ru
          ] as titles,
          directors.full_names as directors,
          kinopoisk_movie.year as year
        FROM
          kinopoisk_movie
        LEFT JOIN
          wikidata_movie
          ON wikidata_movie.kinopoisk_id = kinopoisk_movie.id
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(wikidata_person.full_name_ru) as full_names
            FROM
              wikidata_movie_crew_member
            JOIN
              wikidata_person
              ON wikidata_person.id = wikidata_movie_crew_member.person_id
            WHERE
              wikidata_movie_crew_member.movie_id = wikidata_movie.id
              AND wikidata_movie_crew_member.role = 'director'
          ) "directors"
          ON TRUE
        ORDER BY
          kinopoisk_movie.id ASC;
      `);

      return rows.map((row) => ({
        kinopoiskId: String(row.id),
        directors: (row.directors ?? []).filter((x): x is string => x !== null),
        titles: row.titles.filter((x): x is string => x !== null),
        year: row.year ?? null,
      }));
    });
  }
}
