/* eslint-disable max-classes-per-file */
import isEqual from "lodash.isequal";
import stream from "stream";

import EventBus from "../domain/EventBus.js";

export default class PullWikidataMoviesJob {
  constructor(
    private eventBus: EventBus<Event>,
    private queue: JobQueue,
    private movieRepository: MovieRepository,
    private personRepository: PersonRepository,
    private genreRepository: GenreRepository,
    private wikidataGateway: WikidataGateway,
  ) {}

  async run(): Promise<void> {
    await this.associateWithKinopoiskMovies();
    await this.refreshMovies();
    await this.refreshGenres();
    await this.refreshPeople();
  }

  private async associateWithKinopoiskMovies(): Promise<void> {
    const kinopoiskIds = await this.queue.getJobsQueue();
    const wikidataMovies =
      await this.wikidataGateway.findMoviesByKinopoiskIds(kinopoiskIds);
    const movies = await this.movieRepository.findMany(
      wikidataMovies.map((x) => x.id),
    );
    const updatedMovies = wikidataMovies
      .map((wikidata, index) => {
        let movie =
          movies[index] ?? createMovie(this.eventBus, { id: wikidata.id });
        movie = associateMovieWithKinopoiskId(
          this.eventBus,
          movie,
          wikidata.kinopoiskId,
        );
        return movie;
      })
      .filter((m): m is Movie => m !== null);
    await this.movieRepository.setMany(uniqBy((m) => m.id, updatedMovies));
  }

  private async refreshMovies(): Promise<void> {
    const movies = await this.movieRepository.findAll();
    const wikidataMovies = await this.wikidataGateway.findMoviesByWikidataIds(
      movies.map((m) => m.id),
    );
    const updatedMovies = movies.map((movie, index) => {
      const wikidata = wikidataMovies[index];

      return wikidata ? updateMovie(this.eventBus, movie, wikidata) : movie;
    });

    const peopleUpdates = uniqBy(
      (p) => p.id,
      wikidataMovies
        .filter(
          (x): x is WikidataGatewayFindMoviesByWikidataIdsItem => x !== null,
        )
        .map((m) => m.directors)
        .reduce((acc, x) => [...acc, ...x]),
    );
    const people = await this.personRepository.findMany(
      peopleUpdates.map((p) => p.id),
    );
    const createdPeople = peopleUpdates
      .map((update, index) => {
        const person = people[index];

        return person ? null : createPerson(this.eventBus, update);
      })
      .filter((p): p is Person => p !== null);
    await this.personRepository.setMany(createdPeople);

    const genreUpdates = uniqBy(
      (p) => p.id,
      wikidataMovies
        .filter(
          (x): x is WikidataGatewayFindMoviesByWikidataIdsItem => x !== null,
        )
        .map((m) => m.genres)
        .reduce((acc, x) => [...acc, ...x]),
    );
    const genres = await this.genreRepository.findMany(
      genreUpdates.map((p) => p.id),
    );
    const createdGenres = genreUpdates
      .map((update, index) => {
        const genre = genres[index];

        return genre ? null : createGenre(this.eventBus, update);
      })
      .filter((p): p is Genre => p !== null);
    await this.genreRepository.setMany(createdGenres);

    await this.movieRepository.setMany(updatedMovies);
  }

  private async refreshGenres(): Promise<void> {
    const genres = await this.genreRepository.findAll();
    const wikidataGenres = await this.wikidataGateway.findGenresByWikidataIds(
      genres.map((m) => m.id),
    );
    const updatedGenres = genres.map((person, index) => {
      const wikidata = wikidataGenres[index];

      if (!wikidata) {
        return person;
      }

      return updateGenre(this.eventBus, person, wikidata);
    });

    await this.genreRepository.setMany(updatedGenres);
  }

  private async refreshPeople(): Promise<void> {
    const people = await this.personRepository.findAll();
    const wikidataPeople = await this.wikidataGateway.findPeopleByWikidataIds(
      people.map((m) => m.id),
    );
    const updatedPeople = people.map((person, index) => {
      const wikidata = wikidataPeople[index];

      if (!wikidata) {
        return person;
      }

      return updatePerson(this.eventBus, person, wikidata);
    });

    await this.personRepository.setMany(updatedPeople);
  }
}

function uniqBy<T>(fn: (t: T) => string, items: T[]): T[] {
  const hashes = new Set<string>();
  const result: T[] = [];

  items.forEach((item) => {
    const hash = fn(item);

    if (!hashes.has(hash)) {
      result.push(item);
      hashes.add(hash);
    }
  });

  return result;
}

export interface GenreCreatedEvent {
  payload: {
    id: string;
  };
  type: "GenreCreatedEvent";
}

export interface GenreLabelUpdatedEvent {
  payload: {
    id: string;
    prevLabel: string | null;
    label: string | null;
    locale: "en" | "ru";
  };
  type: "GenreLabelUpdatedEvent";
}

export interface GenreWikipediaSlugUpdatedEvent {
  payload: {
    id: string;
    prevSlug: string | null;
    slug: string | null;
    locale: "en" | "ru";
  };
  type: "GenreWikipediaSlugUpdatedEvent";
}

export interface PersonCreatedEvent {
  payload: {
    id: string;
  };
  type: "PersonCreatedEvent";
}

export interface PersonFullNameUpdatedEvent {
  payload: {
    id: string;
    prevFullName: string | null;
    fullName: string | null;
    locale: "en" | "ru";
  };
  type: "PersonFullNameUpdatedEvent";
}

export interface PersonTmdbIdUpdatedEvent {
  payload: {
    id: string;
    prevTmdbId: string | null;
    tmdbId: string | null;
  };
  type: "PersonTmdbIdUpdatedEvent";
}

export interface PersonWikipediaSlugUpdatedEvent {
  payload: {
    id: string;
    prevSlug: string | null;
    slug: string | null;
    locale: "en" | "ru";
  };
  type: "PersonWikipediaSlugUpdatedEvent";
}

export interface MovieCreatedEvent {
  payload: {
    id: string;
  };
  type: "MovieCreatedEvent";
}

export interface MovieTitleUpdatedEvent {
  payload: {
    id: string;
    prevTitle: string | null;
    title: string | null;
    locale: "en" | "ru";
  };
  type: "MovieTitleUpdatedEvent";
}

export interface MovieKinopoiskIdUpdatedEvent {
  payload: {
    id: string;
    prevKinopoiskId: string | null;
    kinopoiskId: string | null;
  };
  type: "MovieKinopoiskIdUpdatedEvent";
}

export interface MovieImdbIdUpdatedEvent {
  payload: {
    id: string;
    prevImdbId: string | null;
    imdbId: string | null;
  };
  type: "MovieImdbIdUpdatedEvent";
}

export interface MovieDirectorsUpdatedEvent {
  payload: {
    id: string;
  };
  type: "MovieDirectorsUpdatedEvent";
}

export interface MovieDurationUpdatedEvent {
  payload: {
    id: string;
    prevDurationMins: number | null;
    durationMins: number | null;
  };
  type: "MovieDurationUpdatedEvent";
}

export interface MovieGenresUpdatedEvent {
  payload: {
    id: string;
  };
  type: "MovieGenresUpdatedEvent";
}

export interface MovieTmdbIdUpdatedEvent {
  payload: {
    id: string;
    prevTmdbId: string | null;
    tmdbId: string | null;
  };
  type: "MovieTmdbIdUpdatedEvent";
}

export interface MovieWikipediaSlugUpdatedEvent {
  payload: {
    id: string;
    prevSlug: string | null;
    slug: string | null;
    locale: "en" | "ru";
  };
  type: "MovieWikipediaSlugUpdatedEvent";
}

export type Event =
  | GenreCreatedEvent
  | GenreLabelUpdatedEvent
  | GenreWikipediaSlugUpdatedEvent
  | PersonCreatedEvent
  | PersonFullNameUpdatedEvent
  | PersonTmdbIdUpdatedEvent
  | PersonWikipediaSlugUpdatedEvent
  | MovieCreatedEvent
  | MovieTitleUpdatedEvent
  | MovieKinopoiskIdUpdatedEvent
  | MovieImdbIdUpdatedEvent
  | MovieDirectorsUpdatedEvent
  | MovieDurationUpdatedEvent
  | MovieGenresUpdatedEvent
  | MovieTmdbIdUpdatedEvent
  | MovieWikipediaSlugUpdatedEvent;

export interface Movie {
  id: string;
  title: {
    en?: string;
    ru?: string;
  };
  kinopoiskId?: string;
  imdbId?: string;
  directors: {
    id: string;
  }[];
  duration?: {
    minutes: number;
  };
  genres: {
    id: string;
  }[];
  tmdbId?: string;
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export interface Person {
  id: string;
  fullName: {
    en?: string;
    ru?: string;
  };
  tmdbId?: string;
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export interface Genre {
  id: string;
  label: {
    en?: string;
    ru?: string;
  };
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export interface CreatePersonArgs {
  id: string;
}

export function createPerson(
  eventBus: EventBus<Event>,
  create: CreatePersonArgs,
): Person {
  const person: Person = {
    id: create.id,
    fullName: {},
    wikipediaSlugs: {},
  };
  const event: PersonCreatedEvent = {
    payload: {
      id: person.id,
    },
    type: "PersonCreatedEvent",
  };

  eventBus.push(event);

  return person;
}

export interface UpdatePersonArgs {
  id: string;
  fullName: {
    en?: string;
    ru?: string;
  };
  tmdbId?: string;
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export function updatePerson(
  eventBus: EventBus<Event>,
  person: Person,
  update: UpdatePersonArgs,
): Person {
  const updated: Person = update;

  if (person.fullName.en !== updated.fullName.en) {
    const event: PersonFullNameUpdatedEvent = {
      payload: {
        id: updated.id,
        prevFullName: person.fullName.en ?? null,
        fullName: updated.fullName.en ?? null,
        locale: "en",
      },
      type: "PersonFullNameUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (person.fullName.ru !== updated.fullName.ru) {
    const event: PersonFullNameUpdatedEvent = {
      payload: {
        id: updated.id,
        prevFullName: person.fullName.ru ?? null,
        fullName: updated.fullName.ru ?? null,
        locale: "ru",
      },
      type: "PersonFullNameUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (person.tmdbId !== updated.tmdbId) {
    const event: PersonTmdbIdUpdatedEvent = {
      payload: {
        id: updated.id,
        prevTmdbId: person.tmdbId ?? null,
        tmdbId: updated.tmdbId ?? null,
      },
      type: "PersonTmdbIdUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (person.wikipediaSlugs.en !== updated.wikipediaSlugs.en) {
    const event: PersonWikipediaSlugUpdatedEvent = {
      payload: {
        id: updated.id,
        prevSlug: person.wikipediaSlugs.en ?? null,
        slug: updated.wikipediaSlugs.en ?? null,
        locale: "en",
      },
      type: "PersonWikipediaSlugUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (person.wikipediaSlugs.ru !== updated.wikipediaSlugs.ru) {
    const event: PersonWikipediaSlugUpdatedEvent = {
      payload: {
        id: updated.id,
        prevSlug: person.wikipediaSlugs.ru ?? null,
        slug: updated.wikipediaSlugs.ru ?? null,
        locale: "ru",
      },
      type: "PersonWikipediaSlugUpdatedEvent",
    };

    eventBus.push(event);
  }

  return updated;
}

export interface CreateMovieArgs {
  id: string;
}

export function createMovie(
  eventBus: EventBus<Event>,
  create: CreateMovieArgs,
): Movie {
  const movie: Movie = {
    id: create.id,
    title: {},
    directors: [],
    genres: [],
    wikipediaSlugs: {},
  };
  const event: MovieCreatedEvent = {
    payload: {
      id: movie.id,
    },
    type: "MovieCreatedEvent",
  };

  eventBus.push(event);

  return movie;
}

export function associateMovieWithKinopoiskId(
  eventBus: EventBus<Event>,
  movie: Movie,
  kinopoiskId: string,
): Movie {
  const updated: Movie = {
    ...movie,
    kinopoiskId,
  };

  if (movie.kinopoiskId !== updated.kinopoiskId) {
    const event: MovieKinopoiskIdUpdatedEvent = {
      payload: {
        id: updated.id,
        prevKinopoiskId: movie.kinopoiskId ?? null,
        kinopoiskId: updated.kinopoiskId ?? null,
      },
      type: "MovieKinopoiskIdUpdatedEvent",
    };

    eventBus.push(event);
  }

  return updated;
}

export interface UpdateMovieArgs {
  id: string;
  title: {
    en?: string;
    ru?: string;
  };
  kinopoiskId?: string;
  imdbId?: string;
  directors: {
    id: string;
  }[];
  duration?: {
    minutes: number;
  };
  genres: {
    id: string;
  }[];
  tmdbId?: string;
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export function updateMovie(
  eventBus: EventBus<Event>,
  movie: Movie,
  update: UpdateMovieArgs,
): Movie {
  const updated: Movie = {
    id: update.id,
    title: update.title,
    kinopoiskId: update.kinopoiskId,
    imdbId: update.imdbId,
    directors: update.directors.map((director) => ({
      id: director.id,
    })),
    duration: update.duration,
    genres: update.genres.map((genre) => ({
      id: genre.id,
    })),
    tmdbId: update.tmdbId,
    wikipediaSlugs: update.wikipediaSlugs,
  };

  if (movie.title.en !== updated.title.en) {
    const event: MovieTitleUpdatedEvent = {
      payload: {
        id: updated.id,
        prevTitle: movie.title.en ?? null,
        title: updated.title.en ?? null,
        locale: "en",
      },
      type: "MovieTitleUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.title.ru !== updated.title.ru) {
    const event: MovieTitleUpdatedEvent = {
      payload: {
        id: updated.id,
        prevTitle: movie.title.ru ?? null,
        title: updated.title.ru ?? null,
        locale: "ru",
      },
      type: "MovieTitleUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.duration?.minutes !== updated.duration?.minutes) {
    const event: MovieDurationUpdatedEvent = {
      payload: {
        id: updated.id,
        prevDurationMins: movie.duration?.minutes ?? null,
        durationMins: updated.duration?.minutes ?? null,
      },
      type: "MovieDurationUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (!isEqual(movie.directors, updated.directors)) {
    const event: MovieDirectorsUpdatedEvent = {
      payload: {
        id: updated.id,
      },
      type: "MovieDirectorsUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (!isEqual(movie.genres, updated.genres)) {
    const event: MovieGenresUpdatedEvent = {
      payload: {
        id: updated.id,
      },
      type: "MovieGenresUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.imdbId !== updated.imdbId) {
    const event: MovieImdbIdUpdatedEvent = {
      payload: {
        id: updated.id,
        prevImdbId: movie.imdbId ?? null,
        imdbId: updated.imdbId ?? null,
      },
      type: "MovieImdbIdUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.kinopoiskId !== updated.kinopoiskId) {
    const event: MovieKinopoiskIdUpdatedEvent = {
      payload: {
        id: updated.id,
        prevKinopoiskId: movie.kinopoiskId ?? null,
        kinopoiskId: updated.kinopoiskId ?? null,
      },
      type: "MovieKinopoiskIdUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.tmdbId !== updated.tmdbId) {
    const event: MovieTmdbIdUpdatedEvent = {
      payload: {
        id: updated.id,
        prevTmdbId: movie.tmdbId ?? null,
        tmdbId: updated.tmdbId ?? null,
      },
      type: "MovieTmdbIdUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.wikipediaSlugs.en !== updated.wikipediaSlugs.en) {
    const event: MovieWikipediaSlugUpdatedEvent = {
      payload: {
        id: updated.id,
        prevSlug: movie.wikipediaSlugs.en ?? null,
        slug: updated.wikipediaSlugs.en ?? null,
        locale: "en",
      },
      type: "MovieWikipediaSlugUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.wikipediaSlugs.ru !== updated.wikipediaSlugs.ru) {
    const event: MovieWikipediaSlugUpdatedEvent = {
      payload: {
        id: updated.id,
        prevSlug: movie.wikipediaSlugs.ru ?? null,
        slug: updated.wikipediaSlugs.ru ?? null,
        locale: "ru",
      },
      type: "MovieWikipediaSlugUpdatedEvent",
    };

    eventBus.push(event);
  }

  return updated;
}

export interface CreateGenreArgs {
  id: string;
}

export function createGenre(
  eventBus: EventBus<Event>,
  create: CreateGenreArgs,
): Genre {
  const person: Genre = {
    id: create.id,
    label: {},
    wikipediaSlugs: {},
  };
  const event: PersonCreatedEvent = {
    payload: {
      id: person.id,
    },
    type: "PersonCreatedEvent",
  };

  eventBus.push(event);

  return person;
}

export interface UpdateGenreArgs {
  id: string;
  label: {
    en?: string;
    ru?: string;
  };
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export function updateGenre(
  eventBus: EventBus<Event>,
  genre: Genre,
  update: UpdateGenreArgs,
): Genre {
  const updated: Genre = update;

  if (genre.label.en !== updated.label.en) {
    const event: GenreLabelUpdatedEvent = {
      payload: {
        id: updated.id,
        prevLabel: genre.label.en ?? null,
        label: updated.label.en ?? null,
        locale: "en",
      },
      type: "GenreLabelUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (genre.label.ru !== updated.label.ru) {
    const event: GenreLabelUpdatedEvent = {
      payload: {
        id: updated.id,
        prevLabel: genre.label.ru ?? null,
        label: updated.label.ru ?? null,
        locale: "ru",
      },
      type: "GenreLabelUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (genre.wikipediaSlugs.en !== updated.wikipediaSlugs.en) {
    const event: GenreWikipediaSlugUpdatedEvent = {
      payload: {
        id: updated.id,
        prevSlug: genre.wikipediaSlugs.en ?? null,
        slug: updated.wikipediaSlugs.en ?? null,
        locale: "en",
      },
      type: "GenreWikipediaSlugUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (genre.wikipediaSlugs.ru !== updated.wikipediaSlugs.ru) {
    const event: GenreWikipediaSlugUpdatedEvent = {
      payload: {
        id: updated.id,
        prevSlug: genre.wikipediaSlugs.ru ?? null,
        slug: updated.wikipediaSlugs.ru ?? null,
        locale: "ru",
      },
      type: "GenreWikipediaSlugUpdatedEvent",
    };

    eventBus.push(event);
  }

  return updated;
}

export class EventBusLogger implements EventBus<Event> {
  constructor(private wstream: stream.Writable) {}

  push(event: Event): void {
    if (event.type === "GenreCreatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Genre has been created (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "GenreLabelUpdatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Genre "${
          event.payload.prevLabel
        }" has been renamed into "${event.payload.label}" (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "GenreWikipediaSlugUpdatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Genre ${
          event.payload.locale
        } Wikipedia slug changed from "${event.payload.prevSlug}" to "${
          event.payload.slug
        }" (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "PersonCreatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Person has been created (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "PersonTmdbIdUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Person TMDb id has been changed to "${
          event.payload.tmdbId
        }" (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "PersonFullNameUpdatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Person "${
          event.payload.prevFullName
        }" has been renamed into "${event.payload.fullName}" (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "PersonWikipediaSlugUpdatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Person ${
          event.payload.locale
        } wikipedia slug changed from "${event.payload.prevSlug}" to "${
          event.payload.slug
        }" (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "MovieCreatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The movie has been created (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "MovieDirectorsUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Movie directors has been updated (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "MovieDurationUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Movie duration has been updated from ${
          event.payload.prevDurationMins
        } to ${event.payload.durationMins} minutes (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "MovieGenresUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Movie genres has been updated (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "MovieImdbIdUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Movie IMDb id has been changed to "${
          event.payload.imdbId
        }" (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "MovieKinopoiskIdUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Movie KinoPoisk id has been changed to "${
          event.payload.kinopoiskId
        }" (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "MovieTmdbIdUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] Movie TMDb id has been changed to "${
          event.payload.tmdbId
        }" (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "MovieWikipediaSlugUpdatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Movie ${
          event.payload.locale
        } wikipedia slug changed from "${event.payload.prevSlug}" to "${
          event.payload.slug
        }" (id: ${event.payload.id})\n`,
      );
    } else {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Movie "${
          event.payload.prevTitle
        }" has been renamed into "${event.payload.title}" (id: ${
          event.payload.id
        })\n`,
      );
    }
  }
}

export interface JobQueue {
  getJobsQueue(): Promise<string[]>;
}

export interface MovieRepository {
  findAll(): Promise<Movie[]>;
  findMany(wikidataIds: string[]): Promise<(Movie | null)[]>;
  setMany(movies: Movie[]): Promise<void>;
}

export interface PersonRepository {
  findAll(): Promise<Person[]>;
  findMany(wikidataIds: string[]): Promise<(Person | null)[]>;
  setMany(people: Person[]): Promise<void>;
}

export interface GenreRepository {
  findAll(): Promise<Genre[]>;
  findMany(wikidataIds: string[]): Promise<(Genre | null)[]>;
  setMany(genres: Genre[]): Promise<void>;
}

export interface WikidataGateway {
  findMoviesByKinopoiskIds(
    kinopoiskIds: string[],
  ): Promise<WikidataGatewayFindMoviesByKinopoiskIdsResponse>;
  findMoviesByWikidataIds(
    wikidataIds: string[],
  ): Promise<WikidataGatewayFindMoviesByWikidataIdsResponse>;
  findGenresByWikidataIds(
    wikidataIds: string[],
  ): Promise<WikidataGatewayFindGenresByWikidataIdsResponse>;
  findPeopleByWikidataIds(
    wikidataIds: string[],
  ): Promise<WikidataGatewayFindPeopleByWikidataIdsResponse>;
}

export interface WikidataGatewayFindMoviesByKinopoiskIdsItem {
  id: string;
  kinopoiskId: string;
}

export type WikidataGatewayFindMoviesByKinopoiskIdsResponse =
  WikidataGatewayFindMoviesByKinopoiskIdsItem[];

export interface WikidataGatewayFindMoviesByWikidataIdsItem {
  id: string;
  title: {
    en?: string;
    ru?: string;
  };
  kinopoiskId?: string;
  imdbId?: string;
  directors: {
    id: string;
  }[];
  genres: {
    id: string;
  }[];
  duration?: {
    minutes: number;
  };
  tmdbId?: string;
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export type WikidataGatewayFindMoviesByWikidataIdsResponse =
  (WikidataGatewayFindMoviesByWikidataIdsItem | null)[];

export interface WikidataGatewayFindGenresByWikidataIdsItem {
  id: string;
  label: {
    en?: string;
    ru?: string;
  };
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export type WikidataGatewayFindGenresByWikidataIdsResponse =
  (WikidataGatewayFindGenresByWikidataIdsItem | null)[];

export interface WikidataGatewayFindPeopleByWikidataIdsItem {
  id: string;
  fullName: {
    en?: string;
    ru?: string;
  };
  tmdbId?: string;
  wikipediaSlugs: {
    en?: string;
    ru?: string;
  };
}

export type WikidataGatewayFindPeopleByWikidataIdsResponse =
  (WikidataGatewayFindPeopleByWikidataIdsItem | null)[];
