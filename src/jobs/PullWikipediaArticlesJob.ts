import assert from "assert";
import shuffle from "lodash.shuffle";

export default class PullWikipediaArticlesJob {
  constructor(
    private articleRepository: ArticleRepository,
    private queue: JobQueue,
    private wikimediaGateway: WikimediaGateway,
    private debug: (message: string) => void,
  ) {}

  async run(): Promise<void> {
    await this.pullNewArticles();
    await this.pullRandomArticles();
  }

  private async pullNewArticles(): Promise<void> {
    const queue = await this.queue.getJobsQueue();
    const currentRefs = await this.articleRepository.findManyRefs(queue);
    const newRefs = queue.filter((ref, index) => currentRefs[index] === null);

    if (newRefs.length > 0) {
      this.debug(`Pulling ${newRefs.length} new articles`);
    }

    for (const newRef of newRefs) {
      const rawArticle =
        await this.wikimediaGateway.getWikipediaArticle(newRef);

      if (rawArticle) {
        const article: Article = {
          lang: newRef.lang,
          slug: newRef.slug,
          html: rawArticle.html,
          plot: rawArticle.plot,
          genres: rawArticle.genres,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await this.articleRepository.set(article);
      }
    }
  }

  private async pullRandomArticles(): Promise<void> {
    let refs = await this.articleRepository.findAllRefs();

    refs = shuffle(refs).slice(0, 200);

    this.debug(`Pulling ${refs.length} random existing articles`);

    const articles = await this.articleRepository.findMany(refs);

    for (let article of articles) {
      assert(article, "Expected article to exist");

      const rawArticle = await this.wikimediaGateway.getWikipediaArticle(
        article,
        article.updatedAt,
      );

      if (rawArticle) {
        article = {
          lang: article.lang,
          slug: article.slug,
          html: rawArticle.html,
          genres: rawArticle.genres,
          plot: rawArticle.plot,
          createdAt: article.createdAt,
          updatedAt: new Date(),
        };

        await this.articleRepository.set(article);
      }
    }
  }

  private async reparsePlots(): Promise<void> {
    const refs = await this.articleRepository.findAllRefs();

    this.debug(`Reparsing plots`);

    for (const chunk of chunks(refs, 200)) {
      const articles = await this.articleRepository.findMany(chunk);

      for (let article of articles) {
        assert(article, "Expected article to exist");

        const plot = this.wikimediaGateway.parsePlotFromHtml(article.html);

        article = {
          lang: article.lang,
          slug: article.slug,
          html: article.html,
          genres: article.genres,
          plot,
          createdAt: article.createdAt,
          updatedAt: new Date(),
        };

        await this.articleRepository.set(article);
      }
    }
  }
}

export interface WikipediaArticleRef {
  lang: "en" | "ru";
  slug: string;
}

export interface JobQueue {
  getJobsQueue(): Promise<WikipediaArticleRef[]>;
}

export interface Article {
  lang: "en" | "ru";
  slug: string;
  html: string;
  genres: {
    lang: "en" | "ru";
    slug: string;
  }[];
  plot: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ArticleRef {
  lang: "en" | "ru";
  slug: string;
}

export interface ArticleRepository {
  findAllRefs(): Promise<ArticleRef[]>;
  findManyRefs(refs: ArticleRef[]): Promise<(ArticleRef | null)[]>;
  findMany(refs: ArticleRef[]): Promise<(Article | null)[]>;
  set(article: Article): Promise<void>;
}

export interface WikipediaArticle {
  html: string;
  plot: string | null;
  genres: {
    lang: "en" | "ru";
    slug: string;
  }[];
}

export interface WikimediaGateway {
  getWikipediaArticle(
    ref: WikipediaArticleRef,
    ifModifiedSince?: Date,
  ): Promise<WikipediaArticle | null>;
  parsePlotFromHtml(html: string): string | null;
}

function chunks<T>(list: T[], size: number): T[][] {
  const cnks: T[][] = [];
  let chunk: T[] = [];

  list.forEach((item, index) => {
    chunk.push(item);

    if (chunk.length === size || index === list.length - 1) {
      cnks.push(chunk);
      chunk = [];
    }
  });

  return cnks;
}
