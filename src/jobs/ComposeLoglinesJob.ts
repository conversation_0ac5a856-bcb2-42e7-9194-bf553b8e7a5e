import EventBus from "../domain/EventBus";

/* eslint-disable max-classes-per-file */
export default class ComposeLoglinesJob {
  constructor(
    private eventBus: EventBus<Event>,
    private loglineRepository: LoglineRepository,
    private queue: <PERSON><PERSON><PERSON><PERSON>,
    private composer: <PERSON><PERSON><PERSON><PERSON><PERSON>pose<PERSON>,
    private debug: (message: string) => void,
  ) {}

  async run(): Promise<void> {
    const jobs = await this.queue.getJobsQueue();

    if (jobs.length > 0) {
      this.debug(`Composing ${jobs.length} new loglines`);
    }

    for (const job of jobs) {
      try {
        const logline = await this.composer.composeLogline(job.wikipedia.plot);

        await this.loglineRepository.set({
          wikipedia: {
            lang: job.wikipedia.lang,
            slug: job.wikipedia.slug,
          },
          createdAt: new Date(),
          logline: logline.logline,
          model: logline.model,
          prompt: logline.prompt,
          system: logline.system,
          options: logline.options,
        });

        this.eventBus.push({
          payload: {
            wikipedia: {
              lang: job.wikipedia.lang,
              slug: job.wikipedia.slug,
            },
            logline: logline.logline,
          },
          type: "LoglineComposedEvent",
        });
        this.debug(
          `Composed a logline for "${job.wikipedia.slug}": ${logline.logline}`,
        );
      } catch (error) {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        if (error instanceof LoglineComposeError) {
          this.debug(
            `Failed to compose a logline for "${job.wikipedia.slug}". ${error.message}:\n${error.payload}`,
          );
        } else {
          console.error(error);
          await new Promise((resolve) => {
            setTimeout(resolve, 10_000);
          });
        }
      }
    }
  }
}

export interface LoglineComposedEvent {
  payload: {
    wikipedia: {
      lang: "en" | "ru";
      slug: string;
    };
    logline: string;
  };
  type: "LoglineComposedEvent";
}

export type Event = LoglineComposedEvent;

export interface Logline {
  wikipedia: {
    lang: "en" | "ru";
    slug: string;
  };
  logline: string;
  createdAt: Date;
  model: string;
  prompt: string;
  system: string;
  options: Record<string, unknown>;
}

export interface LoglineRepository {
  set(logline: Logline): Promise<void>;
}

export interface ComposeLoglineJob {
  wikipedia: {
    lang: "en" | "ru";
    slug: string;
    plot: string;
  };
}

export interface JobQueue {
  getJobsQueue(): Promise<ComposeLoglineJob[]>;
}

export interface ComposeLoglineResult {
  logline: string;
  model: string;
  prompt: string;
  system: string;
  options: Record<string, unknown>;
}

export class LoglineComposeError extends Error {
  constructor(
    message: string,
    public payload: string,
  ) {
    super(message);
  }
}

export interface LoglineComposer {
  composeLogline(plot: string): Promise<ComposeLoglineResult>;
}
