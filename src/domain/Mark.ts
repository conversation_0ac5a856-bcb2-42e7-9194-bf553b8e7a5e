import assert from "node:assert";
import { differenceInHours, startOfMinute } from "date-fns";

type Mark =
  | {
      mark: number;
      movieId: string;
      timestamp: Date;
      type: "mark";
    }
  | {
      movieId: string;
      timestamp: Date;
      type: "view";
    };
export default Mark;

export function addMarks(existingMarks: Mark[], newMarks: Mark[]): Mark[] {
  const marks: Mark[] = [...existingMarks, ...newMarks];

  restoreMarksOrder(marks);

  const mergedMarks: Mark[] = [];
  const prevViews = new Map<Mark["movieId"], Mark>();
  const prevMarks = new Map<Mark["movieId"], Mark>();

  for (const mark of marks) {
    const prevMark =
      mark.type === "mark"
        ? prevMarks.get(mark.movieId)
        : prevViews.get(mark.movieId);

    if (prevMark && shouldMergeMarks(prevMark, mark)) {
      mergedMarks.splice(mergedMarks.indexOf(prevMark), 1);
    }

    mergedMarks.push(mark);

    if (mark.type === "mark") {
      prevMarks.set(mark.movieId, mark);
    } else {
      prevViews.set(mark.movieId, mark);
    }
  }

  return mergedMarks;
}

export function trackMovieView(
  marks: Mark[],
  movieId: string,
  now: Date,
): Mark[] {
  const newMark: Mark = {
    movieId,
    // Case: mark a movie as viewed and then go to KinoPoisk and give it a mark.
    // Because KinoPoisk provides us with a precision up to a minute, it might
    // violate causation of events, so we conform to this limitation by rounding
    // the timestamp to the closes minute.
    timestamp: startOfMinute(now),
    type: "view",
  };

  return addMarks(marks, [newMark]);
}

export function canTrackNewViewNow(initialMarks: Mark[], now: Date): boolean {
  const marks = [...initialMarks];

  restoreMarksOrder(marks);
  marks.reverse();

  const lastView = marks.find((mark) => mark.type === "view");

  if (!lastView) {
    return true;
  }

  const potentialView: Mark = {
    movieId: lastView.movieId,
    timestamp: now,
    type: "view",
  };

  return !shouldMergeMarks(lastView, potentialView);
}

function shouldMergeMarks(prevMark: Mark, nextMark: Mark): boolean {
  assert(
    prevMark.movieId === nextMark.movieId,
    "Only marks for the same movie can be merged",
  );
  assert(
    prevMark.timestamp.getTime() <= nextMark.timestamp.getTime(),
    "Expected next mark to appear after previous mark",
  );

  return (
    prevMark.type === nextMark.type &&
    differenceInHours(nextMark.timestamp, prevMark.timestamp) <= 48
  );
}

function restoreMarksOrder(marks: Mark[]): void {
  marks.sort(
    (a, b) =>
      a.timestamp.getTime() - b.timestamp.getTime() ||
      (a.type === "mark" ? 1 : 0) - (b.type === "mark" ? 1 : 0),
  );
}
