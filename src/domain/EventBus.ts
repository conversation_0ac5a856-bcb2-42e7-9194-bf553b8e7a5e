export default interface EventBus<E> {
  push(event: E): void;
}

export interface BatchEventBus<E> extends EventBus<E> {
  batch(): Promise<void>;
}

export class SequentBatchEventBus<E> implements BatchEventBus<E> {
  constructor(private eventBuses: (EventBus<E> | BatchEventBus<E>)[]) {}

  push(event: E): void {
    for (const eventBus of this.eventBuses) {
      eventBus.push(event);
    }
  }

  async batch(): Promise<void> {
    for (const eventBus of this.eventBuses) {
      if ("batch" in eventBus) {
        await eventBus.batch();
      }
    }
  }
}
