export interface WatchlistItem {
  timestamp: Date;
  movieId: string;
}

type Watchlist = WatchlistItem[];

export default Watchlist;

export function addToWatchlist(
  watchlist: Watchlist,
  movieId: string,
  now: Date,
): Watchlist {
  if (watchlist.some((item) => item.movieId === movieId)) {
    return watchlist;
  }

  const newWatchlistItem: WatchlistItem = {
    timestamp: now,
    movieId,
  };

  return [...watchlist, newWatchlistItem];
}

export function removeFromWatchlist(
  watchlist: Watchlist,
  movieId: string,
): Watchlist {
  return watchlist.filter((item) => item.movieId !== movieId);
}
