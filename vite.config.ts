import react from "@vitejs/plugin-react";
import { fileURLToPath } from "url";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      input: {
        index: fileURLToPath(new URL("./src/web/index.html", import.meta.url)),
        "5xx": fileURLToPath(new URL("./src/web/5xx.html", import.meta.url)),
      },
    },
  },
});
