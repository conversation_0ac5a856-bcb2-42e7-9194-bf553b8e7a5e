import assert from "assert";

import PullWikipediaArticlesJob from "../src/jobs/PullWikipediaArticlesJob.js";
import ConnectionPool from "../src/postgres/ConnectionPool.js";
import PostgresWikimediaArticleRepository from "../src/postgres/PostgresWikimediaArticleRepository.js";
import PostgresWikimediaJobQueue from "../src/postgres/PostgresWikimediaJobQueue.js";
import RestWikimediaGateway from "../src/wikimedia/RestWikimediaGateway.js";

async function runScript(): Promise<void> {
  assert(
    process.env.POSTGRES_CONNECTION_STRING,
    "Missing POSTGRES_CONNECTION_STRING env variable",
  );

  debug("Wikimedia");

  const pool = new ConnectionPool(process.env.POSTGRES_CONNECTION_STRING);
  const articleRepository = new PostgresWikimediaArticleRepository(pool);
  const queue = new PostgresWikimediaJobQueue(pool);
  const wikimediaGateway = new RestWikimediaGateway({
    accessToken: process.env.WIKIMEDIA_ACCESS_TOKEN,
  });
  const job = new PullWikipediaArticlesJob(
    articleRepository,
    queue,
    wikimediaGateway,
    debug,
  );

  await job.run();

  debug("Done");
}

runScript().catch((error: unknown) => {
  debug(error instanceof Error ? error.stack ?? error.message : String(error));
  process.exit(1);
});

function debug(message: string): void {
  process.stderr.write(
    `[${new Date().toTimeString().slice(0, 8)}] ${message}\n`,
  );
}
