import assert from "node:assert";
import fs from "node:fs";
import http from "node:http";

import server from "../src/web/_server.js";

assert(process.env.PORT, "Missing PORT env variable");
assert(process.env.INDEX_HTML_PATH, "Missing INDEX_HTML_PATH env variable");
assert(process.env.ERROR_HTML_PATH, "Missing ERROR_HTML_PATH env variable");

const indexHtmlPath = process.env.INDEX_HTML_PATH;
const errorHtmlPath = process.env.ERROR_HTML_PATH;

http
  .createServer((req, res) => {
    void server(req, res, {
      indexHtml: fs.readFileSync(indexHtmlPath, "utf-8"),
      errorHtml: fs.readFileSync(errorHtmlPath, "utf-8"),
    });
  })
  .listen(Number(process.env.PORT));
