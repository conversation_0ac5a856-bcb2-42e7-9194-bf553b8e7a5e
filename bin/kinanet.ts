import * as path from "path";
import assert from "assert";

import FiveThousandMoviesBookReader from "../src/kinanet/FiveThousandMoviesBookReader.js";
import KinanetMovieAssociator from "../src/kinanet/KinanetMovieAssociator.js";
import ConnectionPool from "../src/postgres/ConnectionPool.js";
import {
  fillMoviesSubtitles,
  FillMoviesSubtitlesInput,
} from "../src/postgres/KinanetSubtitleRepository.js";
import PostgresKinanetMovieRepository from "../src/postgres/PostgresKinanetMovieRepository.js";

(async () => {
  assert(
    process.env.KINANET_PDF_MARKS_DIRECTORY_PATH,
    "Missing KINANET_PDF_MARKS_DIRECTORY_PATH env variable",
  );
  assert(
    process.env.POSTGRES_CONNECTION_STRING,
    "Missing POSTGRES_CONNECTION_STRING env variable",
  );

  const reader = new FiveThousandMoviesBookReader([
    path.join(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH, "Том 1 (А-В).pdf"),
    path.join(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH, "Том 2 (Г-З).pdf"),
    path.join(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH, "Том 3 (И-М).pdf"),
    path.join(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH, "Том 4 (Н-П).pdf"),
    path.join(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH, "Том 5 (Р-Т).pdf"),
    path.join(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH, "Том 6 (У-Z).pdf"),
  ]);
  const articles = await reader.readArticles();
  const pool = new ConnectionPool(process.env.POSTGRES_CONNECTION_STRING);
  const movieRepository = new PostgresKinanetMovieRepository(pool);
  const associator = new KinanetMovieAssociator(movieRepository);
  const movies = await associator.associateMovies(articles);

  await pool.transaction(async (connection) => {
    await fillMoviesSubtitles(
      connection,
      articles
        .map((article, index): FillMoviesSubtitlesInput | null =>
          movies[index]
            ? {
                kinopoiskId: movies[index]!.kinopoiskId,
                subtitle: article.genre,
              }
            : null,
        )
        .filter((value): value is FillMoviesSubtitlesInput => value != null),
    );
  });
})().catch(console.error);
