import * as fs from "node:fs";
import * as path from "node:path";
import assert from "assert";
import docopt from "docopt";

import { SequentBatchEventBus } from "../src/domain/EventBus.js";
import PullKinopoiskMarksJob, {
  EventBusLogger,
} from "../src/jobs/PullKinopoiskMarksJob.js";
import KinanetKinopoiskGatewayAdapter from "../src/kinanet/KinanetKinopoiskGatewayAdapter.js";
import KinanetMovieAssociator from "../src/kinanet/KinanetMovieAssociator.js";
import MarksSheetReader from "../src/kinanet/MarksSheetReader.js";
import ConnectionPool from "../src/postgres/ConnectionPool.js";
import PostgresEventHandler from "../src/postgres/PostgresEventHandler.js";
import PostgresKinanetMovieRepository from "../src/postgres/PostgresKinanetMovieRepository.js";
import PostgresKinopoiskJobQueue from "../src/postgres/PostgresKinopoiskJobQueue.js";
import PostgresKinopoiskMovieRepository from "../src/postgres/PostgresKinopoiskMovieRepository.js";
import PostgresKinopoiskUserRepository from "../src/postgres/PostgresKinopoiskUserRepository.js";
import TelegramChannelReader from "../src/telegram/TelegramChannelReader.js";
import WebdriverKinopoiskGateway from "../src/webdriver/WebdriverKinopoiskGateway.js";

const options = docopt.docopt(
  `
Kinopoisk

Usage:
  kinopoisk
  kinopoisk [--url=<url>] [--keep-previous-friends]

Options:
  --url=<url>              Crawl only specified Kinopoisk URL.
  --keep-previous-friends  Don't mutate friends list even if it changes on Kinopoisk.
  -h --help                Show this screen.
`.trim(),
  { argv: process.argv.slice(2) },
) as CliOptions;

async function runScript(): Promise<void> {
  assert(
    process.env.KINANET_PDF_MARKS_DIRECTORY_PATH,
    "Missing KINANET_PDF_MARKS_DIRECTORY_PATH env variable",
  );
  assert(
    process.env.TELEGRAM_CACHE_DIRECTORY_PATH,
    "Missing TELEGRAM_CACHE_DIRECTORY_PATH env variable",
  );
  assert(
    process.env.POSTGRES_CONNECTION_STRING,
    "Missing POSTGRES_CONNECTION_STRING env variable",
  );

  const pool = new ConnectionPool(process.env.POSTGRES_CONNECTION_STRING);
  const webdriverKinopoiskGateway = new WebdriverKinopoiskGateway({
    puppeteer: {
      executablePath: process.env.CHROME_EXECUTABLE_PATH,
      userDataDir: process.env.CHROME_USER_DATA_PATH,
    },
  });
  const movieRepository = new PostgresKinopoiskMovieRepository(pool);
  const userRepository = new PostgresKinopoiskUserRepository(pool);
  const eventBus = new SequentBatchEventBus([
    new PostgresEventHandler(debug, pool),
    new EventBusLogger(process.stdout),
  ]);
  const kinanetMarksSheetReader = new MarksSheetReader();
  const kinanetMovieRepository = new PostgresKinanetMovieRepository(pool);
  const kinanetMovieAssociator = new KinanetMovieAssociator(
    kinanetMovieRepository,
  );
  const kinanetTelegramChannelReader = new TelegramChannelReader(
    "sergeykudr56",
    process.env.TELEGRAM_CACHE_DIRECTORY_PATH,
  );
  const kinopoiskGateway = new KinanetKinopoiskGatewayAdapter(
    kinanetMarksSheetReader,
    kinanetMovieAssociator,
    kinanetTelegramChannelReader,
    webdriverKinopoiskGateway,
    fs
      .readdirSync(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH)
      .map((filename) =>
        path.join(process.env.KINANET_PDF_MARKS_DIRECTORY_PATH!, filename),
      ),
  );
  const jobQueue = new PostgresKinopoiskJobQueue(pool);
  const service = new PullKinopoiskMarksJob(
    debug,
    eventBus,
    jobQueue,
    kinopoiskGateway,
    movieRepository,
    userRepository,
  );

  debug("Kinopoisk");
  try {
    if (options["--url"]) {
      await service.refreshUsers({
        keepPreviousFriends: options["--keep-previous-friends"],
        url: options["--url"],
      });
    } else {
      await service.refreshUsers({
        keepPreviousFriends: options["--keep-previous-friends"],
      });
    }
    await eventBus.batch();
  } finally {
    await webdriverKinopoiskGateway.terminate();
  }

  return debug("Done");
}

runScript().catch((error: unknown) => {
  debug(error instanceof Error ? error.stack ?? error.message : String(error));
  process.exit(1);
});

function debug(message: string): void {
  process.stderr.write(
    `[${new Date().toTimeString().slice(0, 8)}] ${message}\n`,
  );
}

type CliOptions =
  | {
      "--keep-previous-friends": boolean;
      "--url": string;
    }
  | {
      "--keep-previous-friends": boolean;
      "--url": null;
    };
