import assert from "node:assert";
import fs from "node:fs";
import http from "node:http";
import path from "node:path";
import { fileURLToPath } from "url";
import { createServer as createViteServer, ViteDevServer } from "vite";

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

assert(process.env.PORT, "Missing PORT env variable");

const vitePromise: Promise<ViteDevServer> = createViteServer({
  configFile: path.resolve(dirname, "..", "vite.config.ts"),
  root: path.resolve(dirname, "..", "src", "web"),
  appType: "custom",
  server: {
    middlewareMode: true,
  },
});

http
  .createServer((req, res) => {
    void (async () => {
      const vite = await vitePromise;

      vite.middlewares(req, res, async (error: unknown) => {
        if (!error) {
          const [indexHtml, errorHtml, { default: server }] = await Promise.all(
            [
              vite.transformIndexHtml(
                req.url ?? "/",
                fs.readFileSync(
                  path.join(dirname, "..", "src", "web", "index.html"),
                  "utf-8",
                ),
              ),
              vite.transformIndexHtml(
                req.url ?? "/",
                fs.readFileSync(
                  path.join(dirname, "..", "src", "web", "5xx.html"),
                  "utf-8",
                ),
              ),
              vite.ssrLoadModule("/_server"),
            ],
          );

          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          void server(req, res, { indexHtml, errorHtml });
        }
      });
    })();
  })
  .listen(Number(process.env.PORT));
