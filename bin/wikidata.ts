import assert from "assert";

import { SequentBatchEventBus } from "../src/domain/EventBus.js";
import PullWikidataMoviesJob, {
  EventBusLogger,
} from "../src/jobs/PullWikidataMoviesJob.js";
import ConnectionPool from "../src/postgres/ConnectionPool.js";
import PostgresEventHandler from "../src/postgres/PostgresEventHandler.js";
import PostgresWikidataGenreRepository from "../src/postgres/PostgresWikidataGenreRepository.js";
import PostgresWikidataJobQueue from "../src/postgres/PostgresWikidataJobQueue.js";
import PostgresWikidataMovieRepository from "../src/postgres/PostgresWikidataMovieRepository.js";
import PostgresWikidataPersonRepository from "../src/postgres/PostgresWikidataPersonRepository.js";
import WikidataSparqlGateway from "../src/wikidata/WikidataSparqlGateway.js";

async function runScript(): Promise<void> {
  assert(
    process.env.POSTGRES_CONNECTION_STRING,
    "Missing POSTGRES_CONNECTION_STRING env variable",
  );

  debug("Wikidata");

  const pool = new ConnectionPool(process.env.POSTGRES_CONNECTION_STRING);
  const movieRepository = new PostgresWikidataMovieRepository(pool);
  const genreRepository = new PostgresWikidataGenreRepository(pool);
  const personRepository = new PostgresWikidataPersonRepository(pool);
  const gateway = new WikidataSparqlGateway();
  const eventBus = new SequentBatchEventBus([
    new PostgresEventHandler(debug, pool),
    new EventBusLogger(process.stdout),
  ]);
  const queue = new PostgresWikidataJobQueue(pool);
  const job = new PullWikidataMoviesJob(
    eventBus,
    queue,
    movieRepository,
    personRepository,
    genreRepository,
    gateway,
  );

  await job.run();
  await eventBus.batch();

  debug("Done");
}

runScript().catch((error: unknown) => {
  debug(error instanceof Error ? error.stack ?? error.message : String(error));
  process.exit(1);
});

function debug(message: string): void {
  process.stderr.write(
    `[${new Date().toTimeString().slice(0, 8)}] ${message}\n`,
  );
}
