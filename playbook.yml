---
- hosts:
    - beelink
  roles:
    - role: betula
      tags:
        - betula
      vars:
        betula_directory_path: "/home/<USER>/betula/"
        betula_database_backup_path: "/mnt/sdb1/backups/links.betula"
        betula_database_path: "/home/<USER>/betula/db/links.betula"
    - role: ssh_port_forward
      tags:
        - ssh_port_forward
    - role: syncthing
      tags:
        - syncthing
    - role: transmission
      tags:
        - transmission
    - role: zyr
      vars:
        chrome_executable_path: "/snap/bin/chromium"
        chrome_user_data_path: "/data/zyr/kinopoisk/chrome-user-data"
        cookie_signing_key: c946bc5ca2be6cbc12532699f09576b1
        deepseek_api_key: 
        images_path: "/data/zyr/nginx/img/"
        kinanet_pdf_marks_directory_path: "/data/zyr/kinopoisk/kinanet"
        kinopoisk_stderr_path: "/data/zyr/kinopoisk/logs/stderr.log"
        kinopoisk_stdout_path: "/data/zyr/kinopoisk/logs/stdout.log"
        llm_stderr_path: "/data/zyr/llm/logs/stderr.log"
        llm_stdout_path: "/data/zyr/llm/logs/stdout.log"
        nginx_img: "/data/zyr/nginx/img/"
        nginx_logs: "/data/zyr/nginx/logs"
        nginx_static: "/data/zyr/nginx/www"
        port: 8001
        postgres_backup_path: "/mnt/sdb1/backups/zyr.sql"
        postgres_connection_string: "socket://nikityy:rejglkdsjg8534843@/var/run/postgresql?db=zyr"
        suggester_url: "http://127.0.0.1:8001"
        telegram_cache_directory_path: "/data/zyr/telegram/cache"
        tmdb_api_key: "4bdea947cda97452be24cb51aa3b46fc"
        tmdb_stderr_path: "/data/zyr/tmdb/logs/stderr.log"
        tmdb_stdout_path: "/data/zyr/tmdb/logs/stdout.log"
        wikidata_stderr_path: "/data/zyr/wikidata/logs/stderr.log"
        wikidata_stdout_path: "/data/zyr/wikidata/logs/stdout.log"
        wikimedia_access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJhdWQiOiI0Yjc0ZGE0ZGFmMGQwNjg3N2E4YTU1MmNjZWQyYzQ2MSIsImp0aSI6IjZmMWZkZGU3MGI1M2ExNmExNzhlYjVmZTcwODc3ODQ4MjY2MGY0YjI5ZWZlYjJiMzJiZTI2MTliNGNjMDVhZTI5YzE1YjI5YzlkYTkwZmM3IiwiaWF0IjoxNzE4MjIxMDczLjYyMzgyMSwibmJmIjoxNzE4MjIxMDczLjYyMzgyNSwiZXhwIjozMzI3NTEyOTg3My42MjA0Niwic3ViIjoiNzU4NjU1NDIiLCJpc3MiOiJodHRwczovL21ldGEud2lraW1lZGlhLm9yZyIsInJhdGVsaW1pdCI6eyJyZXF1ZXN0c19wZXJfdW5pdCI6NTAwMCwidW5pdCI6IkhPVVIifSwic2NvcGVzIjpbImJhc2ljIl19.WRg72DHMmP-zxsnfz7ZE_PePVtZe8P45UX9Kp3ux41AlRq0pB7TP3iw7YX8Ai6LuoqWbXU-fpurTKeczKnmIidm9u0n-gRRaOc7TeDOkRicD1vWsL2epz53412USpLq4voe9bJDlwcQ7jVNfBNmVIRceeBLGU3V3gAdeGdNGcFW1gcdKfMGItB9zJBdXve9N-al9jEozr2hUIyOEt0CXmJtX8JW36yeJwXca0ZNJHtQKeFaKlafgwyKAqyaAkSWKpJA4yTd6acphe_uOiFAJwuQEawJ38oJqnUdyx3jgVgZFBi5lbA9-Q91xRlKBLrgqfinEFG_PK9G86Jy5eMDcytscO1Ar0-CgHHolxmtqgwgwjwaeWtBm8ciuOxYbjrRnrKJHLNdkzFBXH1ZDLd1To6rh0XJePbdnnUtAHQvWuta_CnZQWRIl7RKuB7xTXvqf6AFrs1yOJHU3N-VOMBErzpkK2Cn4BF7jsAx6Da6HQzww4HJKg_rDqIfTSGkYgC8aUOaeUHiN3oxm55QtrhrSNhGFJ0ZKooMfFWgYBh7VREEcCIG34fuBA7HTBsjsDDwHJhhoP7Wjs5oIfoSIFRJJ3bo_hbQd8lTj4TW19ewg3VCPMYhnSNvXGklw6lnQz46jAlGOS3U_XDbfZPHS95cYAUexqXlsPPSqIASl1rDpRc8"
        wikimedia_stderr_path: "/data/zyr/wikimedia/logs/stderr.log"
        wikimedia_stdout_path: "/data/zyr/wikimedia/logs/stdout.log"
- hosts:
    - raspberry
  roles:
    - role: ingress
    - role: aleksivanchenko
      vars:
        app_json_path: /home/<USER>/aleksivanchenko/fashione.json
        dest_path: /data/www/
        dir: /home/<USER>/aleksivanchenko
        domain: fashione.by
        source_path: /data/shared/fashione.by
      tags:
        - aleksivanchenko
        - fashione.by
    - role: aleksivanchenko
      vars:
        app_json_path: /home/<USER>/aleksivanchenko/weds.json
        dest_path: /data/www-weds/
        domain: weds.by
        dir: /home/<USER>/aleksivanchenko
        source_path: /data/shared/weds.by
      tags:
        - aleksivanchenko
        - weds.by
