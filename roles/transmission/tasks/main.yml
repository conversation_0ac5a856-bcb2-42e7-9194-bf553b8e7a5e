---
- name: install transmission
  ansible.builtin.apt:
    name: transmission
    update_cache: yes
    state: present
  become: true
- name: add '{{ ansible_ssh_user }}' to group debian-transmission
  ansible.builtin.user:
    name: '{{ ansible_ssh_user }}'
    groups: debian-transmission
    append: yes
  become: true
- name: create downloads directory
  ansible.builtin.file:
    path: /mnt/sdb1/transmission/
    owner: debian-transmission
    group: debian-transmission
    mode: '0755'
    state: directory
  become: true
- name: create a symbolic link
  ansible.builtin.file:
    src: /mnt/sdb1/transmission/
    dest: /var/lib/transmission-daemon/downloads
    owner: debian-transmission
    group: debian-transmission
    mode: '0755'
    state: link
  become: true
