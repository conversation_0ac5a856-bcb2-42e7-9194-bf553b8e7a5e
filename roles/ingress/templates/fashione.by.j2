server {
  server_name .fashione.by;
  root /data/www/;
}

server {
  listen 443 ssl;
  server_name .fashione.by;
  access_log /data/popcorn/nginx/logs/access.log combined;
  error_log /data/popcorn/nginx/logs/error.log;

  ssl_certificate /etc/letsencrypt/live/fashione.by/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/fashione.by/privkey.pem;
  include /etc/letsencrypt/options-ssl-nginx.conf;

  root /data/www;
  #  return 301 http://$server_name$request_uri;
}

