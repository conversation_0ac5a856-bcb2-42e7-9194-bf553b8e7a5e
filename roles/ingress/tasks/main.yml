---
- name: copy links.nikityy.me config
  ansible.builtin.template:
    src: links.nikityy.me.j2
    dest: "/etc/nginx/sites-enabled/links.nikityy.me"
  become: true
  notify:
    - restart nginx
  tags:
    - betula
- name: copy fashione.by config
  ansible.builtin.template:
    src: fashione.by.j2
    dest: "/etc/nginx/sites-enabled/fashione.by"
  become: true
  notify:
    - restart nginx
  tags:
    - aleksivanchenko
    - fashione.by
- name: copy weds.by config
  ansible.builtin.template:
    src: weds.by.j2
    dest: "/etc/nginx/sites-enabled/weds.by"
  become: true
  notify:
    - restart nginx
  tags:
    - aleksivanchenko
    - weds.by
