server {
  listen 80;
  listen [::]:80;
  server_name links.nikityy.me;

  location / {
    proxy_pass http://127.0.0.1:6996;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $server_name;
    #proxy_pass_request_headers on;
    #proxy_http_version 1.1;
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_types application/json text/html;
    add_header Cache-Control no-cache;
  }
}

server {
  server_name .links.nikityy.me;
  listen 443 ssl http2; # managed by Certbot
  listen [::]:443 ssl http2;
  ssl_certificate /etc/letsencrypt/live/links.nikityy.me/fullchain.pem; # managed by Certbot
  ssl_certificate_key /etc/letsencrypt/live/links.nikityy.me/privkey.pem; # managed by Certbot
  include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
  ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

  location / {
    proxy_pass http://127.0.0.1:6996;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $server_name;
    #proxy_pass_request_headers on;
    #proxy_http_version 1.1;
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_types application/json text/html;
    add_header Cache-Control no-cache;
  }
}
