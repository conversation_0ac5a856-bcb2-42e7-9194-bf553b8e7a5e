---
- name: create a directory
  ansible.builtin.file:
    path: "{{ betula_directory_path }}"
    state: directory
- name: copy executable files
  ansible.builtin.template:
    src: "{{ item }}"
    dest: "{{ betula_directory_path }}"
  loop:
    - docker-compose.yaml
    - Dockerfile
    - start.sh
  notify:
    - restart nikityy.me.service
- name: copy nikityy.me.service
  ansible.builtin.template:
    src: systemd.service
    dest: "/etc/systemd/system/nikityy.me.service"
  become: true
  notify:
    - restart nikityy.me.service
- name: set up backups
  ansible.builtin.cron:
    cron_file: betula
    name: backup
    weekday: "6"
    hour: "0"
    minute: "0"
    user: nikityy
    job: "cp {{ betula_database_path }} {{ betula_database_backup_path }}.$(date --rfc-3339=date)"
  become: true
- name: enable service
  ansible.builtin.systemd_service:
    name: nikityy.me
    enabled: true
  become: true
- name: copy /etc/nginx/sites-enabled/links.nikityy.me
  ansible.builtin.template:
    src: nginx.conf
    dest: "/etc/nginx/sites-enabled/links.nikityy.me"
  become: true
  notify:
    - restart nginx
