---
- name: build bundle
  ansible.builtin.shell:
    cmd: "npm run build:wikidata"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy bundle
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/wikidata.js"
    dest: "{{ ansible_env.HOME }}/wikidata.js"
    recursive: true
    use_ssh_args: true
- name: download node.js
  ansible.builtin.unarchive:
    src: "http://nodejs.org/dist/{{ node_version }}/{{ node_exec_variant }}.tar.gz"
    dest: "{{ ansible_env.HOME }}"
    creates: "{{ node_bin_path }}"
    remote_src: true
- name: create cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: wikidata
    hour: "*"
    minute: "20"
    user: nikityy
    job: "POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ node_bin_path }}/node {{ ansible_env.HOME }}/wikidata.js >> {{ wikidata_stdout_path }} 2>> {{ wikidata_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/9e1d8511-3d39-4e76-a774-e4c3ceca0fff"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: wikidata-logrotate-cron
    dest: "/etc/logrotate.d/cron-wikidata"
  become: true
  notify:
    - restart logrotate
