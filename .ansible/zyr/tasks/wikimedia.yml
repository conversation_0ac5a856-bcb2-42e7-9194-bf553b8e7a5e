---
- name: build bundle
  ansible.builtin.shell:
    cmd: "npm run build:wikimedia"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy bundle
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/wikimedia.js"
    dest: "{{ ansible_env.HOME }}/wikimedia.js"
    recursive: true
    use_ssh_args: true
- name: download node.js
  ansible.builtin.unarchive:
    src: "http://nodejs.org/dist/{{ node_version }}/{{ node_exec_variant }}.tar.gz"
    dest: "{{ ansible_env.HOME }}"
    creates: "{{ node_bin_path }}"
    remote_src: true
- name: create wikimedia.sh
  ansible.builtin.copy:
    dest: "{{ ansible_env.HOME }}/wikimedia.sh"
    mode: "711"
    content: |
      POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} WIKIMEDIA_ACCESS_TOKEN={{ wikimedia_access_token }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ node_bin_path }}/node {{ ansible_env.HOME }}/wikimedia.js >> {{ wikimedia_stdout_path }} 2>> {{ wikimedia_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/30df1323-3202-4f56-acbe-7a0798d5a3f4
- name: create cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: wikimedia
    hour: "*"
    minute: "20"
    user: nikityy
    job: "{{ ansible_env.HOME }}/wikimedia.sh"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: wikimedia-logrotate-cron
    dest: "/etc/logrotate.d/cron-wikimedia"
  become: true
  notify:
    - restart logrotate
