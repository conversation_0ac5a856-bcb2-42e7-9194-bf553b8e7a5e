---
- name: build bundle
  ansible.builtin.shell:
    cmd: "npm run build:llm"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy bundle
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/llm.js"
    dest: "{{ ansible_env.HOME }}/llm.js"
    recursive: true
    use_ssh_args: true
- name: download node.js
  ansible.builtin.unarchive:
    src: "http://nodejs.org/dist/{{ node_version }}/{{ node_exec_variant }}.tar.gz"
    dest: "{{ ansible_env.HOME }}"
    creates: "{{ node_bin_path }}"
    remote_src: true
- name: create cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: llm
    hour: "19,20,21,22,23,0,1,2"
    minute: "30"
    user: nikityy
    job: "POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} DEEPSEEK_API_KEY={{ deepseek_api_key }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ node_bin_path }}/node {{ ansible_env.HOME }}/llm.js >> {{ llm_stdout_path }} 2>> {{ llm_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/bdbfb9a9-9d08-47e7-9098-926206c43792"
  vars:
    node_version: "v18.20.3"
    node_exec_variant: "node-{{ node_version }}-{{ ansible_facts.system | lower }}-{{ architecture }}"
    node_bin_path: "{{ ansible_env.HOME }}/{{ node_exec_variant }}/bin"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: llm-logrotate-cron.j2
    dest: "/etc/logrotate.d/cron-llm"
  become: true
  notify:
    - restart logrotate
