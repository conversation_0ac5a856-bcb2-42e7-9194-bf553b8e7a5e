map $cookie_zyr_webauthn_credential_id $webauth_credential_id {
  default $cookie_zyr_webauthn_credential_id;
  ""      "-";
}

log_format zyrlog '$remote_addr - $webauth_credential_id [$time_local] '
                  '"$request" $status $body_bytes_sent '
                  '"$http_referer" "$http_user_agent"';

server {
  listen 80;
  listen [::]:80;
  server_name .zyr.best;
  access_log {{ nginx_logs }}/access.log zyrlog;
  error_log {{ nginx_logs }}/error.log;

  return 301 https://$server_name$request_uri;
}

server {
  listen 443 ssl http2;
  listen [::]:443 ssl http2;
  server_name .zyr.best;
  access_log {{ nginx_logs }}/access.log zyrlog;
  error_log {{ nginx_logs }}/error.log;

  real_ip_header CF-Connecting-IP;
  set_real_ip_from 127.0.0.1;
  set_real_ip_from ************/20;
  set_real_ip_from ************/22;
  set_real_ip_from ************/22;
  set_real_ip_from **********/22;
  set_real_ip_from ************/18;
  set_real_ip_from *************/18;
  set_real_ip_from ************/20;
  set_real_ip_from ************/20;
  set_real_ip_from *************/22;
  set_real_ip_from ************/17;
  set_real_ip_from ***********/15;
  set_real_ip_from **********/13;
  set_real_ip_from **********/14;
  set_real_ip_from **********/13;
  set_real_ip_from **********/22;
  set_real_ip_from 2400:cb00::/32;
  set_real_ip_from 2606:4700::/32;
  set_real_ip_from 2803:f800::/32;
  set_real_ip_from 2405:b500::/32;
  set_real_ip_from 2405:8100::/32;
  set_real_ip_from 2a06:98c0::/29;
  set_real_ip_from 2c0f:f248::/32;

  ssl_certificate /etc/letsencrypt/live/zyr.best/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/zyr.best/privkey.pem;
  include /etc/letsencrypt/options-ssl-nginx.conf;

  location / {
    root {{ nginx_static }};
    index none;
    try_files $uri @proxy;
    sendfile on;
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_types text/css application/javascript;
    expires 1M;
    add_header Cache-Control public;
  }

  location ^~ /img {
    alias {{ nginx_img }};
    sendfile on;
    expires 1M;
    add_header Cache-Control public;
  }

  location @proxy {
    proxy_pass {{ suggester_url }};
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_http_version 1.1;
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_types application/json text/html;
    add_header Cache-Control no-cache;
  }
}
