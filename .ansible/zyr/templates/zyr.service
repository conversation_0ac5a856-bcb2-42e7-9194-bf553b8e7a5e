[Unit]
Description=Zyr Server
After=network.target

[Service]
ExecStart={{ node_bin_path }}/node {{ bundle_remote_path }}
Environment=NODE_ENV=production
Environment=PORT={{ port }}
Environment=POSTGRES_CONNECTION_STRING={{ postgres_connection_string }}
Environment=COOKIE_SIGNING_KEY={{ cookie_signing_key }}
Environment=INDEX_HTML_PATH={{ index_html_path }}
Environment=ERROR_HTML_PATH={{ error_html_path }}
Restart=on-failure
User=nikityy

[Install]
WantedBy=multi-user.target
