const { configure, presets } = require("eslint-kit");

module.exports = configure({
  mode: "only-errors",
  presets: [
    presets.imports({
      sort: {
        newline: true,
        groups: [
          // side effects
          ["^\\u0000"],

          // node.js libraries and scoped libraries
          [
            "^node:.*(/.*)?$",
            "^(child_process|crypto|events|fs|http|https|os|path)(/.*)?$",
            "^@?\\w",
          ],

          // common aliases (@app, @root, @/, ~/) and anything not matched
          ["^@app", "^@root", "^~", "^"],

          // relative imports
          ["^\\."],
        ],
      },
    }),
    presets.typescript(),
    presets.prettier(),
    presets.node(),
    presets.react({ version: "18.0" }),
  ],
  extend: {
    rules: {
      "import-x/no-default-export": "off",
      "react/destructuring-assignment": "off",
      "unicorn/no-abusive-eslint-disable": "off",
      "unicorn/no-nested-ternary": "off",
      "unicorn/number-literal-case": "off",
    },
    ignorePatterns: [
      "dist",
      "client.generated",
      "node_modules",
      "codegen.yml",
      "package.json",
      "tsconfig.json",
      "package-lock.json",
      "README.md",
    ],
  },
});
