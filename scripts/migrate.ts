import path from "path";
import assert from "assert";
import pg from "pg";
import <PERSON><PERSON><PERSON> from "postgrator";
import { fileURLToPath } from "url";

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

(async () => {
  assert(
    process.env.POSTGRES_CONNECTION_STRING,
    "Missing POSTGRES_CONNECTION_STRING env variable",
  );

  const client = new pg.Client({
    connectionString: process.env.POSTGRES_CONNECTION_STRING,
  });

  await client.connect();

  const postgrator = new Postgrator({
    driver: "pg",
    migrationPattern: `${dirname}/../src/postgres/migrations/*`,
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    execQuery: (query) => client.query(query),
  });

  const migrations = await postgrator.migrate("max");

  for (const migration of migrations) {
    process.stdout.write(`${migration.name}\n`);
  }

  await client.end();
})().catch((error) => {
  process.stderr.write(
    `${
      error instanceof Error ? error.stack ?? error.message : String(error)
    }\n`,
  );
  process.exit(1);
});
